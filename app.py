import streamlit as st
import pandas as pd
from stqdm import stqdm
from dotenv import load_dotenv
import os
import time
import re
import zipfile
from io import BytesIO
import google.generativeai as genai
from google.generativeai import types
from google.api_core import exceptions as core_exceptions
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut, GeocoderUnavailable
import pycountry
from nltk.util import ngrams
from nltk.tokenize import word_tokenize
from fuzzywuzzy import fuzz

st.set_page_config(page_title="Country Extraction Tool", layout="centered")

# Ensure NLTK data is available
try:
    word_tokenize("test") # This implicitly loads 'punkt' if available
    import nltk.data
    nltk.data.find('tokenizers/punkt') # Check if 'punkt' itself is found
except LookupError:
    import nltk
    st.info("Downloading NLTK 'punkt' tokenizer data. This may take a moment...")
    try:
        nltk.download('punkt', quiet=True)
        st.success("NLTK 'punkt' tokenizer downloaded.")
    except Exception as e:
        st.error(f"Failed to download NLTK 'punkt' data: {e}")

# Load .env file
load_dotenv()

def get_gemini_api_key_from_env(): # Renamed and corrected
    # load_dotenv() is called globally at the start of the script
    return os.getenv("GEMINI_API_KEY")

# Define a more comprehensive keyword map
# This can be expanded over time
COUNTRY_KEYWORD_MAP = {
    # G7
    "canada": "Canada",
    "france": "France",
    "germany": "Germany",
    "italy": "Italy",
    "japan": "Japan",
    "uk": "United Kingdom",
    "united kingdom": "United Kingdom",
    "britain": "United Kingdom",
    "england": "United Kingdom",
    "scotland": "United Kingdom",
    "wales": "United Kingdom",
    "northern ireland": "United Kingdom",
    "usa": "United States",
    "u.s.a": "United States",
    "u.s.": "United States",
    "united states": "United States",
    "america": "United States",
    # BRICS
    "brazil": "Brazil",
    "russia": "Russia",
    "india": "India",
    "china": "China",
    "south africa": "South Africa",
    # Other populous countries
    "indonesia": "Indonesia",
    "pakistan": "Pakistan",
    "nigeria": "Nigeria",
    "bangladesh": "Bangladesh",
    "mexico": "Mexico",
    "philippines": "Philippines",
    "egypt": "Egypt",
    "vietnam": "Vietnam",
    "turkey": "Turkey", # Note: 'turkey' also a food, context is key
    "iran": "Iran",
    "thailand": "Thailand",
    # Common mentions
    "australia": "Australia",
    "spain": "Spain",
    "argentina": "Argentina",
    "poland": "Poland",
    "ukraine": "Ukraine",
    "saudi arabia": "Saudi Arabia",
    "uae": "United Arab Emirates",
    "united arab emirates": "United Arab Emirates",
    "singapore": "Singapore",
    "malaysia": "Malaysia",
    "switzerland": "Switzerland",
    "sweden": "Sweden",
    "norway": "Norway",
    "denmark": "Denmark",
    "finland": "Finland",
    "netherlands": "Netherlands",
    "holland": "Netherlands",
    "belgium": "Belgium",
    "austria": "Austria",
    "greece": "Greece",
    "portugal": "Portugal",
    "ireland": "Ireland",
    "new zealand": "New Zealand",
    "israel": "Israel",
    "south korea": "South Korea",
    "korea": "South Korea", # Ambiguous, but often refers to South Korea in many contexts
    "maldives": "Maldives",
    "bahamas": "Bahamas",
    "corona island": "Colombia",
    # Cities that strongly imply countries
    "london": "United Kingdom",
    "paris": "France",
    "berlin": "Germany",
    "tokyo": "Japan",
    "beijing": "China",
    "moscow": "Russia",
    "delhi": "India",
    "new delhi": "India",
    "mumbai": "India",
    "rome": "Italy",
    "madrid": "Spain",
    "toronto": "Canada",
    "sydney": "Australia",
    "south australia": "Australia",
    "new york": "United States",
    "los angeles": "United States",
    "chicago": "United States",
}

# Minimum similarity score for fuzzy matching against our gazetteer
FUZZY_MATCH_THRESHOLD = 88 # Adjust as needed (0-100)

def generate_ngrams(text, n_values=[1, 2, 3]):
    """Generates n-grams from the input text for specified n values."""
    if not text or not isinstance(text, str):
        return []
    # Remove punctuation that might interfere with n-gram generation or matching
    # but keep apostrophes for names like "Cote d'Ivoire"
    # and hyphens for names like "Guinea-Bissau"
    text_cleaned = re.sub(r'[.,!?;:"()]|(?<![a-zA-Z])-(?![a-zA-Z])', ' ', text)
    tokens = word_tokenize(text_cleaned.lower()) # Tokenize and lowercase
    all_ngrams = []
    for n in n_values:
        generated = ngrams(tokens, n)
        all_ngrams.extend([" ".join(gram) for gram in generated])
    # Prioritize longer n-grams by sorting them in descending order of length
    return sorted(list(set(all_ngrams)), key=len, reverse=True)

def validate_country_with_gazetteer_and_fuzzy_search(text, keyword_map, fuzzy_threshold=FUZZY_MATCH_THRESHOLD):
    if not text or not isinstance(text, str) or text.strip().lower() == 'n/a':
        return "N/A"

    # Generate n-grams (1, 2, and 3 words long typically)
    # Prioritize longer n-grams as they are more specific
    search_terms = generate_ngrams(text, n_values=[3, 2, 1])

    # Normalize our keyword map keys to lowercase for consistent comparison
    lower_keyword_map = {k.lower(): v for k, v in keyword_map.items()}
    gazetteer_keywords_sorted = sorted(lower_keyword_map.keys(), key=len, reverse=True)

    # Phase 1: Direct match with our gazetteer (using generated n-grams)
    for term in search_terms:
        if term in lower_keyword_map:
            # st.info(f"Gazetteer direct match: '{term}' -> '{lower_keyword_map[term]}'")
            return lower_keyword_map[term]

    # Phase 2: pycountry fuzzy search (using generated n-grams)
    for term in search_terms:
        try:
            # pycountry's search_fuzzy can return multiple results, we take the first (best)
            matches = pycountry.countries.search_fuzzy(term)
            if matches:
                # We could add a confidence check here if pycountry provided one
                # For now, taking the first match from pycountry is usually good enough
                # st.info(f"pycountry fuzzy match: '{term}' -> '{matches[0].name}'")
                return matches[0].name
        except LookupError: # Happens if term is too short or garbage
            continue
        except Exception as e: # Catch other potential errors from pycountry
            # st.warning(f"pycountry search error for '{term}': {e}")
            continue

    # Phase 3: Fuzzy matching against our gazetteer keys (using generated n-grams)
    for term in search_terms:
        for gazetteer_key in gazetteer_keywords_sorted: # Iterate through our known good keywords
            # Using token_set_ratio is good for phrases with different word orders or extra words
            similarity = fuzz.token_set_ratio(term, gazetteer_key)
            if similarity >= fuzzy_threshold:
                # st.info(f"Gazetteer fuzzy match: '{term}' vs '{gazetteer_key}' ({similarity}%) -> '{lower_keyword_map[gazetteer_key]}'")
                return lower_keyword_map[gazetteer_key]

    return "N/A" # If no validation method yields a result

def validate_country_with_geopy(text, attempt=1, max_attempts=3):
    if not text or not isinstance(text, str) or text.strip().lower() == 'n/a':
        return "N/A"
    try:
        geolocator = Nominatim(user_agent="country_extractor_app/1.0")
        location = geolocator.geocode(text, language='en', addressdetails=True, timeout=10)
        if location and location.raw.get('address') and location.raw['address'].get('country'):
            country_name = location.raw['address']['country']
            # Sometimes geopy returns country codes, try to convert to full name
            try:
                country_obj = (pycountry.countries.get(alpha_2=country_name) or
                               pycountry.countries.get(alpha_3=country_name) or
                               pycountry.countries.get(name=country_name) or
                               pycountry.countries.get(official_name=country_name))
                if country_obj:
                    return country_obj.name
                # If it's already a common name, that's fine
                if pycountry.countries.get(name=country_name.title()):
                     return country_name.title()

            except AttributeError: # Not a standard code, could be full name
                 # Check if the returned name is a valid country name directly
                if pycountry.countries.get(name=country_name.title()):
                     return country_name.title()
            # Fallback if pycountry doesn't find it but geopy gave *something*
            # that looks like a country name (e.g. "United States of America")
            if len(country_name.split()) > 1 or country_name.istitle(): # Heuristic for country names
                return country_name
        return "N/A"
    except GeocoderTimedOut:
        if attempt < max_attempts:
            time.sleep(attempt) # Exponential backoff
            return validate_country_with_geopy(text, attempt + 1, max_attempts)
        st.warning(f"Geopy timed out for text: {text[:50]}...")
        return "N/A"
    except GeocoderUnavailable:
        st.warning(f"Geopy service unavailable for text: {text[:50]}...")
        return "N/A"
    except Exception as e:
        st.warning(f"Geopy error for text '{text[:50]}...': {e}")
        return "N/A"

def validate_country_with_keywords(text, keyword_map):
    if not text or not isinstance(text, str) or text.strip().lower() == 'n/a':
        return "N/A"

    # Convert text to lowercase for case-insensitive matching
    lower_text = text.lower()

    # Sort keywords by length in descending order to match longer phrases first
    # e.g., "united states" before "states" if "states" was a keyword (it isn't here)
    sorted_keywords = sorted(keyword_map.keys(), key=len, reverse=True)

    for keyword in sorted_keywords:
        # Use word boundaries to avoid partial matches (e.g., "usa" in "causal")
        # FIXED: Use single backslash for proper regex
        if re.search(r'\b' + re.escape(keyword) + r'\b', lower_text):
            return keyword_map[keyword]

    return "N/A"

def check_nationality_references(text):
    """Check for nationality words and cultural references that imply countries"""
    if not text or not isinstance(text, str):
        return "N/A"

    text_lower = text.lower()

    # Nationality to country mapping
    nationality_map = {
        'american': 'United States',
        'british': 'United Kingdom',
        'english': 'United Kingdom',
        'french': 'France',
        'german': 'Germany',
        'italian': 'Italy',
        'spanish': 'Spain',
        'japanese': 'Japan',
        'chinese': 'China',
        'indian': 'India',
        'brazilian': 'Brazil',
        'mexican': 'Mexico',
        'canadian': 'Canada',
        'australian': 'Australia',
        'russian': 'Russia',
        'indonesian': 'Indonesia',
        'thai': 'Thailand',
        'filipino': 'Philippines',
        'vietnamese': 'Vietnam',
        'korean': 'South Korea',
        'turkish': 'Turkey',
        'greek': 'Greece',
        'portuguese': 'Portugal',
        'dutch': 'Netherlands',
        'swiss': 'Switzerland',
        'norwegian': 'Norway',
        'swedish': 'Sweden',
        'danish': 'Denmark',
        'finnish': 'Finland',
        'belgian': 'Belgium',
        'austrian': 'Austria',
        'irish': 'Ireland',
        'polish': 'Poland',
        'egyptian': 'Egypt',
        'saudi': 'Saudi Arabia',
        'emirati': 'United Arab Emirates',
        'singaporean': 'Singapore',
        'malaysian': 'Malaysia',
        'maldivian': 'Maldives',
    }

    # Check for nationality words
    for nationality, country in nationality_map.items():
        if re.search(r'\b' + re.escape(nationality) + r'\b', text_lower):
            return country

    return "N/A"

def extract_country_from_text(text):
    if pd.isna(text) or not str(text).strip():
        return "N/A"

    original_text_for_validation = str(text) # Keep original for validators

    # Preprocess text to handle edge cases
    preprocessed_text = preprocess_text_for_edge_cases(original_text_for_validation)
    if preprocessed_text is None:
        return "N/A"

    # IMPROVED: Try keyword matching FIRST for hashtag content
    # This catches obvious cases like #Philippines, #Indonesia, etc.
    country_keywords = validate_country_with_keywords(original_text_for_validation, COUNTRY_KEYWORD_MAP)
    if country_keywords != "N/A":
        return country_keywords

    # Enhanced prompt that specifically mentions hashtags
    prompt = (
        "Given the following text, extract the country or countries mentioned. "
        "Pay special attention to hashtags (words starting with #) as they often contain location information. "
        "If a city, region, or place is mentioned, return the country it is in. "
        "If multiple countries are mentioned, return all as a comma-separated list. "
        "Be thorough and look for both explicit and implicit country references. "
        "Consider cities, regions, nationalities, cultural references, and hashtags. "
        "If no country or location is found, return 'N/A'.\\n\\n"
        f"Text: {preprocessed_text}\\nCountries: "
    )
    country_gemini = "N/A" # Default
    try:

        model = genai.GenerativeModel(model_name='gemini-2.0-flash')
        generation_config = types.GenerationConfig(


            max_output_tokens=128, # Increased for more detailed responses
            temperature=0.1, # Slight randomness to catch edge cases
        )
        response = model.generate_content(
            contents=prompt,
            generation_config=generation_config
        )
        country_gemini = response.text.strip()

        if not country_gemini or country_gemini.lower() == 'n/a':
            country_gemini = "N/A"

    except core_exceptions.ResourceExhausted as e:
        raise RuntimeError("Rate limited by Gemini API. Please try again later or reduce batch size/delay.")
    except Exception as e:
        st.warning(f"Gemini API error for text '{original_text_for_validation[:50]}...': {e}")

        country_gemini = "ERROR: Gemini API failed"




    # IMPROVED: Validate Gemini's output more thoroughly
    if country_gemini != "N/A" and not country_gemini.startswith("ERROR:"):
        # Validate Gemini's output against known country names
        validated_countries = []
        for country in country_gemini.split(','):
            country = country.strip()
            # Try multiple validation approaches
            if pycountry.countries.get(name=country.title()):
                validated_countries.append(country.title())
            elif country.lower() in COUNTRY_KEYWORD_MAP:
                validated_countries.append(COUNTRY_KEYWORD_MAP[country.lower()])
            # Check if it's a fuzzy match for a known country
            else:
                fuzzy_result = validate_country_with_gazetteer_and_fuzzy_search(country, COUNTRY_KEYWORD_MAP)
                if fuzzy_result != "N/A":
                    validated_countries.append(fuzzy_result)

        if validated_countries:
            return ', '.join(validated_countries)

    # Enhanced Validation Steps - try all methods if Gemini fails or gives invalid results
    if country_gemini == "N/A" or country_gemini.startswith("ERROR:") or not country_gemini:

        # Try Geopy next
        country_geopy = validate_country_with_geopy(original_text_for_validation)
        if country_geopy != "N/A":
            return country_geopy

        # Try gazetteer + fuzzy search
        country_gaz_fuzzy = validate_country_with_gazetteer_and_fuzzy_search(original_text_for_validation, COUNTRY_KEYWORD_MAP)
        if country_gaz_fuzzy != "N/A":
            return country_gaz_fuzzy

        # Final attempt: check for nationality words and cultural references
        country_from_nationality = check_nationality_references(original_text_for_validation)
        if country_from_nationality != "N/A":
            return country_from_nationality

        # If Gemini had an error, return that error
        if country_gemini.startswith("ERROR:"):
            return country_gemini

        return "N/A"

    # If we get here, Gemini gave a result but it didn't validate
    # Try fallback methods
    country_geopy = validate_country_with_geopy(original_text_for_validation)
    if country_geopy != "N/A":
        return country_geopy

    country_gaz_fuzzy = validate_country_with_gazetteer_and_fuzzy_search(original_text_for_validation, COUNTRY_KEYWORD_MAP)
    if country_gaz_fuzzy != "N/A":
        return country_gaz_fuzzy

    country_from_nationality = check_nationality_references(original_text_for_validation)
    if country_from_nationality != "N/A":
        return country_from_nationality

    return "N/A"

def get_default_description_column(columns):
    for col in columns:
        if col.strip().lower() == "description":
            return col
    return None

def get_default_title_column(columns):
    for col in columns:
        if col.strip().lower() == "title":
            return col
    return None

def combine_title_and_description(title, description):
    """Combine title and description text for processing"""
    title_text = str(title).strip() if pd.notna(title) and str(title).strip() else ""
    desc_text = str(description).strip() if pd.notna(description) and str(description).strip() else ""

    # If both are available, combine them with a separator
    if title_text and desc_text:
        return f"{title_text}. {desc_text}"
    # If only one is available, return that one
    elif title_text:
        return title_text
    elif desc_text:
        return desc_text
    # If neither is available, return empty string
    else:
        return ""

def get_output_filename(uploaded_file):
    if hasattr(uploaded_file, 'name'):
        base = uploaded_file.name
    else:
        base = "output.xlsx"
    base = re.sub(r"\.xlsx$", "", base, flags=re.IGNORECASE)
    return f"{base}_with_countries.xlsx"

def preprocess_text_for_edge_cases(text):
    # Remove social media hashtags (REMOVED PER USER REQUEST)
    # cleaned_text = re.sub(r"#\w+", "", text)
    cleaned_text = text
    # Detect sports context: if 'vs' or 'versus' appears along with keywords like 'watch' or 'game', skip extraction
    if re.search(r'\b(vs|versus)\b', cleaned_text, re.I) and re.search(r'\b(watch|watching|game|match)\b', cleaned_text, re.I):
        return None
    # Detect culinary context: if words like 'ate', 'had', 'delicious', 'recipe', 'cooked', etc. appear with ambiguous food terms
    if re.search(r"\b(ate|had|delicious|recipe|cooked)\b", cleaned_text, re.I) and re.search(r"\b(turkey|chile)\b", cleaned_text, re.I):
        return None
    # Detect brand context: if known brand names appear, treat as non-location reference
    if re.search(r"\b(Amazon|Apple|Google)\b", cleaned_text, re.I):
        return None
    return cleaned_text.strip()


st.title("🌍 Country Extraction Tool")
st.write("Upload one or more Excel files, select the title and description columns, and extract country names using Gemini LLM.")

multi_mode = st.checkbox("Process multiple files at once (batch folder mode)")

if multi_mode:
    uploaded_files = st.file_uploader("Upload your Excel files (.xlsx)", type=["xlsx"], accept_multiple_files=True)
else:
    uploaded_files = st.file_uploader("Upload your Excel file (.xlsx)", type=["xlsx"])
    if uploaded_files:
        uploaded_files = [uploaded_files]

if uploaded_files:
    api_key_from_env = get_gemini_api_key_from_env() # Call renamed and corrected function
    if api_key_from_env:
        st.success("Gemini API key loaded from .env file.") # Message is now accurate
        api_key = api_key_from_env
    else:
        st.info("Gemini API key not found in .env file. Please enter it below.") # Better user guidance
        api_key = st.text_input("Enter your Gemini API key:", type="password")
    st.markdown("---")
    st.subheader("Batch Processing Controls (to avoid rate limits)")
    batch_size = st.number_input("Batch size (rows per batch)", min_value=1, max_value=100, value=10)
    delay = st.number_input("Delay between batches (seconds)", min_value=0.0, max_value=60.0, value=2.0, step=0.5)
    st.markdown("---")
    if api_key:
        if st.button("Extract Countries"):
            genai.configure(api_key=api_key)
            zip_buffer = BytesIO()
            with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zipf:
                for uploaded_file in uploaded_files:
                    try:
                        df = pd.read_excel(uploaded_file)
                        columns = df.columns.tolist()

                        st.write(f"**File:** {uploaded_file.name}")
                        st.write("Please select the columns containing the title and description text:")

                        # Title column selection
                        default_title_name = get_default_title_column(columns)
                        default_title_index = columns.index(default_title_name) if default_title_name else 0

                        title_col = st.selectbox(
                            f"Select title column for {uploaded_file.name}",
                            options=["None"] + columns,
                            index=columns.index(default_title_name) + 1 if default_title_name else 0,
                            key=f"title_col_select_{uploaded_file.name}"
                        )

                        # Description column selection
                        default_desc_name = get_default_description_column(columns)
                        default_desc_index = columns.index(default_desc_name) if default_desc_name else 0

                        desc_col = st.selectbox(
                            f"Select description column for {uploaded_file.name}",
                            options=["None"] + columns,
                            index=columns.index(default_desc_name) + 1 if default_desc_name else 0,
                            key=f"desc_col_select_{uploaded_file.name}"
                        )

                        # Validate that at least one column is selected
                        if title_col == "None" and desc_col == "None":
                            st.warning(f"Please select at least one column (title or description) for {uploaded_file.name}. Skipping this file.")
                            continue

                        # Convert "None" selections to None for processing
                        title_col = None if title_col == "None" else title_col
                        desc_col = None if desc_col == "None" else desc_col

                        # Display which columns are being processed
                        columns_used = []
                        if title_col:
                            columns_used.append(f"Title: '{title_col}'")
                        if desc_col:
                            columns_used.append(f"Description: '{desc_col}'")
                        st.write(f"**Processing file:** {uploaded_file.name} using columns: **{', '.join(columns_used)}**")

                        results = []
                        error_occurred = False

                        # Extract data from selected columns
                        titles = df[title_col].tolist() if title_col else [None] * len(df)
                        descriptions = df[desc_col].tolist() if desc_col else [None] * len(df)
                        total = len(df)
                        for start in stqdm(range(0, total, batch_size), desc=f"Processing batches for {uploaded_file.name}"):
                            end = min(start + batch_size, total)
                            title_batch = titles[start:end]
                            desc_batch = descriptions[start:end]

                            for i, (title, desc) in enumerate(zip(title_batch, desc_batch)):
                                # Combine title and description text
                                combined_text = combine_title_and_description(title, desc)

                                if not combined_text.strip():
                                    results.append("N/A")
                                else:
                                    preprocessed = preprocess_text_for_edge_cases(combined_text)
                                    if preprocessed is None:
                                        results.append("N/A")
                                    else:
                                        try:
                                            country = extract_country_from_text(preprocessed)
                                            if country.startswith("ERROR: "):
                                                st.warning(f"Error for row {len(results)+1} in {uploaded_file.name}: {country}")
                                            results.append(country)
                                        except RuntimeError as rte:
                                            st.error(f"{uploaded_file.name}: {str(rte)}")
                                            error_occurred = True
                                            break
                            if error_occurred:
                                break
                            if end < total:
                                time.sleep(delay)
                        if not error_occurred:
                            df["Extracted Country"] = results
                            outname = get_output_filename(uploaded_file)
                            out_buffer = BytesIO()
                            df.to_excel(out_buffer, index=False, engine="openpyxl")
                            out_buffer.seek(0)
                            zipf.writestr(outname, out_buffer.read())
                            st.success(f"Processed: {uploaded_file.name}")
                            st.dataframe(df.head(10))
                    except Exception as e:
                        st.error(f"Error processing {uploaded_file.name}: {e}")
            zip_buffer.seek(0)
            if multi_mode:
                st.download_button(
                    "Download All Processed Files (ZIP)",
                    zip_buffer,
                    file_name="all_with_countries.zip",
                    mime="application/zip",
                )
            else:
                # Single file mode: offer direct download of the one file
                zip_buffer.seek(0)
                with zipfile.ZipFile(zip_buffer) as zipf:
                    for name in zipf.namelist():
                        st.download_button(
                            "Download Updated Excel",
                            zipf.read(name),
                            file_name=name,
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        )
    else:
        st.info("Please provide a Gemini API key.")
