
def check_nationality_references(text):
    """Check for nationality words and cultural references that imply countries"""
    if not text or not isinstance(text, str):
        return "N/A"
    
    text_lower = text.lower()
    
    # Nationality to country mapping
    nationality_map = {
        'american': 'United States',
        'british': 'United Kingdom',
        'english': 'United Kingdom',
        'french': 'France',
        'german': 'Germany',
        'italian': 'Italy',
        'spanish': 'Spain',
        'japanese': 'Japan',
        'chinese': 'China',
        'indian': 'India',
        'brazilian': 'Brazil',
        'mexican': 'Mexico',
        'canadian': 'Canada',
        'australian': 'Australia',
        'russian': 'Russia',
        'indonesian': 'Indonesia',
        'thai': 'Thailand',
        'filipino': 'Philippines',
        'vietnamese': 'Vietnam',
        'korean': 'South Korea',
        'turkish': 'Turkey',
        'greek': 'Greece',
        'portuguese': 'Portugal',
        'dutch': 'Netherlands',
        'swiss': 'Switzerland',
        'norwegian': 'Norway',
        'swedish': 'Sweden',
        'danish': 'Denmark',
        'finnish': 'Finland',
        'belgian': 'Belgium',
        'austrian': 'Austria',
        'irish': 'Ireland',
        'polish': 'Poland',
        'egyptian': 'Egypt',
        'saudi': 'Saudi Arabia',
        'emirati': 'United Arab Emirates',
        'singaporean': 'Singapore',
        'malaysian': 'Malaysia',
        'maldivian': 'Maldives',
    }
    
    # Check for nationality words
    for nationality, country in nationality_map.items():
        if re.search(r'\b' + re.escape(nationality) + r'\b', text_lower):
            return country
    
    return "N/A"
