# Country Extraction Tool

A Streamlit web app for non-coders to extract country names from Excel files containing post descriptions, using Google Gemini LLM.

## Features
- Upload any Excel file (.xlsx)
- Select both title and description columns (or either one)
- Extracts country/countries mentioned in the combined text using Gemini LLM
- Adds a new column with the extracted country/countries
- Download the updated Excel file
- Handles multiple countries, missing data, and errors
- Intelligently combines title and description text for better country extraction

## Setup
1. Clone this repo or copy the files to your machine.
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. (Optional) Increase upload size in `.streamlit/config.toml` if needed.

## Usage
1. Run the app:
   ```bash
   streamlit run app.py
   ```
2. Upload your Excel file.
3. Select the description column.
4. Enter your Gemini API key (get one from Google AI Studio).
5. Click 'Extract Countries'.
6. Download the processed file.

## Getting a Gemini API Key
- Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
- Sign in and create an API key
- Paste the key into the app when prompted

## Requirements
- See `requirements.txt` for Python dependencies
- See `.streamlit/config.toml` for upload size config

---
Built with ❤️ using Streamlit, Pandas, and Google Gemini LLM.