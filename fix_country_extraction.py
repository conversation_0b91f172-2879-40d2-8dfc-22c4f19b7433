#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the country extraction accuracy issues identified in the analysis.
This script will patch the app.py file with improved extraction logic.
"""

import re

def create_improved_extraction_functions():
    """Create the improved extraction functions"""
    
    # Missing function that was being called
    check_nationality_function = '''
def check_nationality_references(text):
    """Check for nationality words and cultural references that imply countries"""
    if not text or not isinstance(text, str):
        return "N/A"
    
    text_lower = text.lower()
    
    # Nationality to country mapping
    nationality_map = {
        'american': 'United States',
        'british': 'United Kingdom',
        'english': 'United Kingdom',
        'french': 'France',
        'german': 'Germany',
        'italian': 'Italy',
        'spanish': 'Spain',
        'japanese': 'Japan',
        'chinese': 'China',
        'indian': 'India',
        'brazilian': 'Brazil',
        'mexican': 'Mexico',
        'canadian': 'Canada',
        'australian': 'Australia',
        'russian': 'Russia',
        'indonesian': 'Indonesia',
        'thai': 'Thailand',
        'filipino': 'Philippines',
        'vietnamese': 'Vietnam',
        'korean': 'South Korea',
        'turkish': 'Turkey',
        'greek': 'Greece',
        'portuguese': 'Portugal',
        'dutch': 'Netherlands',
        'swiss': 'Switzerland',
        'norwegian': 'Norway',
        'swedish': 'Sweden',
        'danish': 'Denmark',
        'finnish': 'Finland',
        'belgian': 'Belgium',
        'austrian': 'Austria',
        'irish': 'Ireland',
        'polish': 'Poland',
        'egyptian': 'Egypt',
        'saudi': 'Saudi Arabia',
        'emirati': 'United Arab Emirates',
        'singaporean': 'Singapore',
        'malaysian': 'Malaysia',
        'maldivian': 'Maldives',
    }
    
    # Check for nationality words
    for nationality, country in nationality_map.items():
        if re.search(r'\\b' + re.escape(nationality) + r'\\b', text_lower):
            return country
    
    return "N/A"
'''

    # Improved keyword validation function (fixes the regex bug)
    improved_keyword_function = '''
def validate_country_with_keywords(text, keyword_map):
    if not text or not isinstance(text, str) or text.strip().lower() == 'n/a':
        return "N/A"

    # Convert text to lowercase for case-insensitive matching
    lower_text = text.lower()

    # Sort keywords by length in descending order to match longer phrases first
    # e.g., "united states" before "states" if "states" was a keyword (it isn't here)
    sorted_keywords = sorted(keyword_map.keys(), key=len, reverse=True)

    for keyword in sorted_keywords:
        # Use word boundaries to avoid partial matches (e.g., "usa" in "causal")
        # FIXED: Use single backslash for proper regex
        if re.search(r'\\b' + re.escape(keyword) + r'\\b', lower_text):
            return keyword_map[keyword]

    return "N/A"
'''

    # Improved extraction function with better logic
    improved_extraction_function = '''
def extract_country_from_text(text):
    if pd.isna(text) or not str(text).strip():
        return "N/A"

    original_text_for_validation = str(text) # Keep original for validators

    # Preprocess text to handle edge cases
    preprocessed_text = preprocess_text_for_edge_cases(original_text_for_validation)
    if preprocessed_text is None:
        return "N/A"

    # IMPROVED: Try keyword matching FIRST for hashtag content
    # This catches obvious cases like #Philippines, #Indonesia, etc.
    country_keywords = validate_country_with_keywords(original_text_for_validation, COUNTRY_KEYWORD_MAP)
    if country_keywords != "N/A":
        return country_keywords

    # Enhanced prompt that specifically mentions hashtags
    prompt = (
        "Given the following text, extract the country or countries mentioned. "
        "Pay special attention to hashtags (words starting with #) as they often contain location information. "
        "If a city, region, or place is mentioned, return the country it is in. "
        "If multiple countries are mentioned, return all as a comma-separated list. "
        "Be thorough and look for both explicit and implicit country references. "
        "Consider cities, regions, nationalities, cultural references, and hashtags. "
        "If no country or location is found, return 'N/A'.\\n\\n"
        f"Text: {preprocessed_text}\\nCountries: "
    )
    
    country_gemini = "N/A" # Default
    try:
        model = genai.GenerativeModel(model_name='gemini-2.0-flash')
        generation_config = types.GenerationConfig(
            max_output_tokens=128, # Increased for more detailed responses
            temperature=0.1, # Slight randomness to catch edge cases
        )
        response = model.generate_content(
            contents=prompt,
            generation_config=generation_config
        )
        country_gemini = response.text.strip()

        if not country_gemini or country_gemini.lower() == 'n/a':
            country_gemini = "N/A"

    except core_exceptions.ResourceExhausted as e:
        raise RuntimeError("Rate limited by Gemini API. Please try again later or reduce batch size/delay.")
    except Exception as e:
        st.warning(f"Gemini API error for text '{original_text_for_validation[:50]}...': {e}")
        country_gemini = "ERROR: Gemini API failed"

    # IMPROVED: Validate Gemini's output more thoroughly
    if country_gemini != "N/A" and not country_gemini.startswith("ERROR:"):
        # Validate Gemini's output against known country names
        validated_countries = []
        for country in country_gemini.split(','):
            country = country.strip()
            # Try multiple validation approaches
            if pycountry.countries.get(name=country.title()):
                validated_countries.append(country.title())
            elif country.lower() in COUNTRY_KEYWORD_MAP:
                validated_countries.append(COUNTRY_KEYWORD_MAP[country.lower()])
            # Check if it's a fuzzy match for a known country
            else:
                fuzzy_result = validate_country_with_gazetteer_and_fuzzy_search(country, COUNTRY_KEYWORD_MAP)
                if fuzzy_result != "N/A":
                    validated_countries.append(fuzzy_result)

        if validated_countries:
            return ', '.join(validated_countries)

    # Enhanced Validation Steps - try all methods if Gemini fails or gives invalid results
    if country_gemini == "N/A" or country_gemini.startswith("ERROR:") or not country_gemini:
        
        # Try Geopy next
        country_geopy = validate_country_with_geopy(original_text_for_validation)
        if country_geopy != "N/A":
            return country_geopy

        # Try gazetteer + fuzzy search
        country_gaz_fuzzy = validate_country_with_gazetteer_and_fuzzy_search(original_text_for_validation, COUNTRY_KEYWORD_MAP)
        if country_gaz_fuzzy != "N/A":
            return country_gaz_fuzzy

        # Final attempt: check for nationality words and cultural references
        country_from_nationality = check_nationality_references(original_text_for_validation)
        if country_from_nationality != "N/A":
            return country_from_nationality

        # If Gemini had an error, return that error
        if country_gemini.startswith("ERROR:"):
            return country_gemini

        return "N/A"

    # If we get here, Gemini gave a result but it didn't validate
    # Try fallback methods
    country_geopy = validate_country_with_geopy(original_text_for_validation)
    if country_geopy != "N/A":
        return country_geopy

    country_gaz_fuzzy = validate_country_with_gazetteer_and_fuzzy_search(original_text_for_validation, COUNTRY_KEYWORD_MAP)
    if country_gaz_fuzzy != "N/A":
        return country_gaz_fuzzy

    country_from_nationality = check_nationality_references(original_text_for_validation)
    if country_from_nationality != "N/A":
        return country_from_nationality

    return "N/A"
'''

    return check_nationality_function, improved_keyword_function, improved_extraction_function

def main():
    """Main function to create the fixes"""
    print("🔧 CREATING COUNTRY EXTRACTION FIXES")
    print("=" * 50)
    
    check_nationality_func, improved_keyword_func, improved_extraction_func = create_improved_extraction_functions()
    
    # Save the functions to separate files for easy integration
    with open("check_nationality_references.py", "w") as f:
        f.write(check_nationality_func)
    
    with open("improved_keyword_validation.py", "w") as f:
        f.write(improved_keyword_func)
    
    with open("improved_extraction_function.py", "w") as f:
        f.write(improved_extraction_func)
    
    print("✅ Created fix files:")
    print("   - check_nationality_references.py")
    print("   - improved_keyword_validation.py") 
    print("   - improved_extraction_function.py")
    
    print("\\n📋 SUMMARY OF FIXES:")
    print("1. ✅ Added missing check_nationality_references() function")
    print("2. ✅ Fixed regex bug in keyword matching (double backslash issue)")
    print("3. ✅ Improved Gemini prompt to emphasize hashtag content")
    print("4. ✅ Enhanced validation of Gemini output")
    print("5. ✅ Reordered processing to try keyword matching first")
    print("6. ✅ Added comprehensive fallback methods")
    
    print("\\n⚠️  NEXT STEPS:")
    print("1. Apply these fixes to app.py")
    print("2. Test the fixes on problematic cases")
    print("3. Re-process the dataset with improved extraction")

if __name__ == "__main__":
    main()
