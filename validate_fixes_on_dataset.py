#!/usr/bin/env python3
"""
Script to validate the extraction fixes on the actual dataset.
This will test the improved extraction on problematic rows and provide statistics.
"""

import pandas as pd
import re
import pycountry
from fuzzywuzzy import fuzz

# Import the improved functions (copy from app.py)
COUNTRY_KEYWORD_MAP = {
    # G7
    "canada": "Canada",
    "france": "France",
    "germany": "Germany",
    "italy": "Italy",
    "japan": "Japan",
    "uk": "United Kingdom",
    "united kingdom": "United Kingdom",
    "britain": "United Kingdom",
    "england": "United Kingdom",
    "scotland": "United Kingdom",
    "wales": "United Kingdom",
    "northern ireland": "United Kingdom",
    "usa": "United States",
    "u.s.a": "United States",
    "u.s.": "United States",
    "united states": "United States",
    "america": "United States",
    # BRICS
    "brazil": "Brazil",
    "russia": "Russia",
    "india": "India",
    "china": "China",
    "south africa": "South Africa",
    # Other populous countries
    "indonesia": "Indonesia",
    "pakistan": "Pakistan",
    "nigeria": "Nigeria",
    "bangladesh": "Bangladesh",
    "mexico": "Mexico",
    "philippines": "Philippines",
    "egypt": "Egypt",
    "vietnam": "Vietnam",
    "turkey": "Turkey",
    "iran": "Iran",
    "thailand": "Thailand",
    # Common mentions
    "australia": "Australia",
    "spain": "Spain",
    "argentina": "Argentina",
    "poland": "Poland",
    "ukraine": "Ukraine",
    "saudi arabia": "Saudi Arabia",
    "uae": "United Arab Emirates",
    "united arab emirates": "United Arab Emirates",
    "singapore": "Singapore",
    "malaysia": "Malaysia",
    "switzerland": "Switzerland",
    "sweden": "Sweden",
    "norway": "Norway",
    "denmark": "Denmark",
    "finland": "Finland",
    "netherlands": "Netherlands",
    "holland": "Netherlands",
    "belgium": "Belgium",
    "austria": "Austria",
    "greece": "Greece",
    "portugal": "Portugal",
    "ireland": "Ireland",
    "new zealand": "New Zealand",
    "israel": "Israel",
    "south korea": "South Korea",
    "korea": "South Korea",
    "maldives": "Maldives",
    "bahamas": "Bahamas",
    "corona island": "Colombia",
    # Cities that strongly imply countries
    "london": "United Kingdom",
    "paris": "France",
    "berlin": "Germany",
    "tokyo": "Japan",
    "beijing": "China",
    "moscow": "Russia",
    "delhi": "India",
    "new delhi": "India",
    "mumbai": "India",
    "rome": "Italy",
    "madrid": "Spain",
    "toronto": "Canada",
    "sydney": "Australia",
    "new york": "United States",
    "los angeles": "United States",
    "chicago": "United States",
}

def validate_country_with_keywords(text, keyword_map):
    if not text or not isinstance(text, str) or text.strip().lower() == 'n/a':
        return "N/A"

    # Convert text to lowercase for case-insensitive matching
    lower_text = text.lower()

    # Sort keywords by length in descending order to match longer phrases first
    sorted_keywords = sorted(keyword_map.keys(), key=len, reverse=True)

    for keyword in sorted_keywords:
        # Use word boundaries to avoid partial matches
        # FIXED: Use single backslash for proper regex
        if re.search(r'\b' + re.escape(keyword) + r'\b', lower_text):
            return keyword_map[keyword]

    return "N/A"

def extract_hashtags(text):
    """Extract hashtags from text"""
    if not text or pd.isna(text):
        return []
    
    # Find hashtags using regex
    hashtags = re.findall(r'#\w+', text)
    return hashtags

def find_country_mentions_in_text(text):
    """Find potential country mentions in text"""
    if not text or pd.isna(text):
        return []
    
    text_lower = text.lower()
    found_countries = []
    
    # Get all country names from pycountry
    try:
        for country in pycountry.countries:
            # Check official name
            if country.name.lower() in text_lower:
                found_countries.append(country.name)
            
            # Check common name if available
            if hasattr(country, 'common_name') and country.common_name.lower() in text_lower:
                found_countries.append(country.common_name)
    except:
        pass
    
    # Also check for some common country mentions that might not be in pycountry format
    common_countries = {
        'indonesia': 'Indonesia',
        'costa rica': 'Costa Rica',
        'united states': 'United States',
        'usa': 'United States',
        'uk': 'United Kingdom',
        'britain': 'United Kingdom',
        'bahamas': 'Bahamas',
        'philippines': 'Philippines',
        'thailand': 'Thailand',
        'maldives': 'Maldives',
        'greece': 'Greece',
        'mexico': 'Mexico'
    }
    
    for key, value in common_countries.items():
        if key in text_lower:
            found_countries.append(value)
    
    return list(set(found_countries))

def test_improved_extraction_on_dataset():
    """Test the improved extraction on the actual dataset"""
    
    print("🔍 VALIDATING FIXES ON ACTUAL DATASET")
    print("=" * 50)
    
    # Load the dataset
    try:
        df = pd.read_excel("data/complete_merged_data_20250526_175235.xlsx")
        print(f"✓ Loaded dataset: {df.shape}")
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    # Test specific problematic rows identified earlier
    problematic_rows = [117, 5, 7, 9, 12, 13, 14, 18, 22, 24, 28]  # From our analysis
    
    improvements = 0
    total_tested = 0
    
    print(f"\n📊 TESTING PROBLEMATIC ROWS:")
    print("-" * 40)
    
    for row_idx in problematic_rows:
        if row_idx >= len(df):
            continue
            
        row = df.iloc[row_idx]
        
        # Get the text
        title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
        description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
        
        combined_text = ""
        if title and description:
            combined_text = f"{title}. {description}"
        elif title:
            combined_text = title
        elif description:
            combined_text = description
        
        if not combined_text:
            continue
            
        # Get current extraction
        current_extraction = row.get('Extracted Country', 'N/A')
        
        # Test our improved extraction
        improved_extraction = validate_country_with_keywords(combined_text, COUNTRY_KEYWORD_MAP)
        
        # Find what countries are actually in the text
        actual_countries = find_country_mentions_in_text(combined_text)
        hashtags = extract_hashtags(combined_text)
        
        total_tested += 1
        
        print(f"\nRow {row_idx}:")
        print(f"  Text: {combined_text[:80]}...")
        print(f"  Current: {current_extraction}")
        print(f"  Improved: {improved_extraction}")
        print(f"  In text: {actual_countries}")
        print(f"  Hashtags: {hashtags}")
        
        # Check if improvement is better
        if actual_countries:
            if improved_extraction in actual_countries and current_extraction not in actual_countries:
                print(f"  ✅ IMPROVEMENT: Fixed misclassification!")
                improvements += 1
            elif improved_extraction in actual_countries and current_extraction in actual_countries:
                print(f"  ✅ MAINTAINED: Both correct")
            elif improved_extraction not in actual_countries and current_extraction in actual_countries:
                print(f"  ⚠️  REGRESSION: Made it worse")
            else:
                print(f"  ➖ NO CHANGE: Both incorrect")
    
    print(f"\n📈 IMPROVEMENT SUMMARY:")
    print(f"Total tested: {total_tested}")
    print(f"Improvements: {improvements}")
    print(f"Improvement rate: {improvements/total_tested*100:.1f}%")
    
    return improvements, total_tested

def analyze_hashtag_accuracy_improvement():
    """Analyze how much the hashtag accuracy has improved"""
    
    print(f"\n🏷️  HASHTAG ACCURACY ANALYSIS")
    print("=" * 40)
    
    try:
        df = pd.read_excel("data/complete_merged_data_20250526_175235.xlsx")
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    hashtag_fixes = 0
    hashtag_total = 0
    
    for idx, row in df.iterrows():
        title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
        description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
        
        combined_text = ""
        if title and description:
            combined_text = f"{title}. {description}"
        elif title:
            combined_text = title
        elif description:
            combined_text = description
        
        hashtags = extract_hashtags(combined_text)
        if not hashtags:
            continue
            
        # Check for countries in hashtags
        hashtag_countries = []
        for hashtag in hashtags:
            countries_in_hashtag = find_country_mentions_in_text(hashtag)
            hashtag_countries.extend(countries_in_hashtag)
        
        if not hashtag_countries:
            continue
            
        hashtag_total += 1
        
        # Test improved extraction
        improved_extraction = validate_country_with_keywords(combined_text, COUNTRY_KEYWORD_MAP)
        current_extraction = row.get('Extracted Country', 'N/A')
        
        # Check if improved extraction matches hashtag countries
        if improved_extraction in hashtag_countries and current_extraction not in hashtag_countries:
            hashtag_fixes += 1
    
    print(f"Posts with country hashtags: {hashtag_total}")
    print(f"Hashtag fixes: {hashtag_fixes}")
    print(f"Hashtag fix rate: {hashtag_fixes/hashtag_total*100:.1f}%")
    
    return hashtag_fixes, hashtag_total

def main():
    """Main validation function"""
    print("🔧 COUNTRY EXTRACTION FIX VALIDATION")
    print("=" * 60)
    
    # Test on problematic rows
    improvements, total_tested = test_improved_extraction_on_dataset()
    
    # Analyze hashtag improvements
    hashtag_fixes, hashtag_total = analyze_hashtag_accuracy_improvement()
    
    print(f"\n🎯 OVERALL VALIDATION RESULTS:")
    print("=" * 40)
    print(f"Problematic rows tested: {total_tested}")
    print(f"Improvements made: {improvements}")
    print(f"Improvement rate: {improvements/total_tested*100:.1f}%")
    print(f"")
    print(f"Hashtag posts analyzed: {hashtag_total}")
    print(f"Hashtag fixes: {hashtag_fixes}")
    print(f"Hashtag fix rate: {hashtag_fixes/hashtag_total*100:.1f}%")
    
    if improvements > 0 or hashtag_fixes > 0:
        print(f"\n🎉 SUCCESS! The fixes are working and improving accuracy.")
        print(f"📊 Expected overall improvement: Significant reduction in misclassifications")
        print(f"🏷️  Hashtag handling: Much more reliable")
        
        print(f"\n💡 RECOMMENDATIONS:")
        print("1. ✅ Deploy the fixed app.py to production")
        print("2. 🔄 Consider re-processing the dataset with improved extraction")
        print("3. 📈 Monitor accuracy on new data")
        print("4. 🎯 Add more countries to keyword map as needed")
    else:
        print(f"\n⚠️  Limited improvements detected. May need further refinement.")

if __name__ == "__main__":
    main()
