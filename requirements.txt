streamlit  # Web app framework
pandas      # Data manipulation and Excel I/O
openpyxl    # Excel file support for pandas
stqdm       # Streamlit progress bar for loops
google-generativeai  # Gemini API client
python-dotenv  # For loading .env files 
geopy >= 2.0 # For geocoding and country extraction
pycountry >= 23.0 # For country name/code normalization 
thefuzz >= 0.20 # For fuzzy string matching
python-Levenshtein >= 0.20 # Improves performance for thefuzz (optional but recommended) 