#!/usr/bin/env python3
"""
Test script to verify that the country extraction fixes work correctly.
Tests the problematic cases identified in the analysis.
"""

import pandas as pd
import re
import pycountry
from fuzzywuzzy import fuzz
from nltk.util import ngrams
from nltk.tokenize import word_tokenize

# Import the keyword map and functions from app.py
FUZZY_MATCH_THRESHOLD = 80

COUNTRY_KEYWORD_MAP = {
    # G7
    "canada": "Canada",
    "france": "France",
    "germany": "Germany",
    "italy": "Italy",
    "japan": "Japan",
    "uk": "United Kingdom",
    "united kingdom": "United Kingdom",
    "britain": "United Kingdom",
    "england": "United Kingdom",
    "scotland": "United Kingdom",
    "wales": "United Kingdom",
    "northern ireland": "United Kingdom",
    "usa": "United States",
    "u.s.a": "United States",
    "u.s.": "United States",
    "united states": "United States",
    "america": "United States",
    # BRICS
    "brazil": "Brazil",
    "russia": "Russia",
    "india": "India",
    "china": "China",
    "south africa": "South Africa",
    # Other populous countries
    "indonesia": "Indonesia",
    "pakistan": "Pakistan",
    "nigeria": "Nigeria",
    "bangladesh": "Bangladesh",
    "mexico": "Mexico",
    "philippines": "Philippines",
    "egypt": "Egypt",
    "vietnam": "Vietnam",
    "turkey": "Turkey",
    "iran": "Iran",
    "thailand": "Thailand",
    # Common mentions
    "australia": "Australia",
    "spain": "Spain",
    "argentina": "Argentina",
    "poland": "Poland",
    "ukraine": "Ukraine",
    "saudi arabia": "Saudi Arabia",
    "uae": "United Arab Emirates",
    "united arab emirates": "United Arab Emirates",
    "singapore": "Singapore",
    "malaysia": "Malaysia",
    "switzerland": "Switzerland",
    "sweden": "Sweden",
    "norway": "Norway",
    "denmark": "Denmark",
    "finland": "Finland",
    "netherlands": "Netherlands",
    "holland": "Netherlands",
    "belgium": "Belgium",
    "austria": "Austria",
    "greece": "Greece",
    "portugal": "Portugal",
    "ireland": "Ireland",
    "new zealand": "New Zealand",
    "israel": "Israel",
    "south korea": "South Korea",
    "korea": "South Korea",
    "maldives": "Maldives",
    "corona island": "Colombia",
    # Cities that strongly imply countries
    "london": "United Kingdom",
    "paris": "France",
    "berlin": "Germany",
    "tokyo": "Japan",
    "beijing": "China",
    "moscow": "Russia",
    "delhi": "India",
    "new delhi": "India",
    "mumbai": "India",
    "rome": "Italy",
    "madrid": "Spain",
    "toronto": "Canada",
    "sydney": "Australia",
    "new york": "United States",
    "los angeles": "United States",
    "chicago": "United States",
}

def validate_country_with_keywords(text, keyword_map):
    if not text or not isinstance(text, str) or text.strip().lower() == 'n/a':
        return "N/A"

    # Convert text to lowercase for case-insensitive matching
    lower_text = text.lower()

    # Sort keywords by length in descending order to match longer phrases first
    sorted_keywords = sorted(keyword_map.keys(), key=len, reverse=True)

    for keyword in sorted_keywords:
        # Use word boundaries to avoid partial matches
        # FIXED: Use single backslash for proper regex
        if re.search(r'\b' + re.escape(keyword) + r'\b', lower_text):
            return keyword_map[keyword]

    return "N/A"

def check_nationality_references(text):
    """Check for nationality words and cultural references that imply countries"""
    if not text or not isinstance(text, str):
        return "N/A"
    
    text_lower = text.lower()
    
    # Nationality to country mapping
    nationality_map = {
        'american': 'United States',
        'british': 'United Kingdom',
        'english': 'United Kingdom',
        'french': 'France',
        'german': 'Germany',
        'italian': 'Italy',
        'spanish': 'Spain',
        'japanese': 'Japan',
        'chinese': 'China',
        'indian': 'India',
        'brazilian': 'Brazil',
        'mexican': 'Mexico',
        'canadian': 'Canada',
        'australian': 'Australia',
        'russian': 'Russia',
        'indonesian': 'Indonesia',
        'thai': 'Thailand',
        'filipino': 'Philippines',
        'vietnamese': 'Vietnam',
        'korean': 'South Korea',
        'turkish': 'Turkey',
        'greek': 'Greece',
        'portuguese': 'Portugal',
        'dutch': 'Netherlands',
        'swiss': 'Switzerland',
        'norwegian': 'Norway',
        'swedish': 'Sweden',
        'danish': 'Denmark',
        'finnish': 'Finland',
        'belgian': 'Belgium',
        'austrian': 'Austria',
        'irish': 'Ireland',
        'polish': 'Poland',
        'egyptian': 'Egypt',
        'saudi': 'Saudi Arabia',
        'emirati': 'United Arab Emirates',
        'singaporean': 'Singapore',
        'malaysian': 'Malaysia',
        'maldivian': 'Maldives',
    }
    
    # Check for nationality words
    for nationality, country in nationality_map.items():
        if re.search(r'\b' + re.escape(nationality) + r'\b', text_lower):
            return country
    
    return "N/A"

def test_problematic_cases():
    """Test the specific problematic cases identified in the analysis"""
    
    test_cases = [
        {
            'text': 'Kick back, relax and enjoy the view.⁣\n\n#ThisIsLiving⁣\n\n📸: @merrwatson⁣\n\n#Philippines',
            'expected': 'Philippines',
            'description': 'Row 117 - #Philippines hashtag'
        },
        {
            'text': 'Tune the world out and find your moment of peace.⁣\n\n#ThisIsLiving⁣\n\n📸: @travelifejournal⁣\n\n#FreedomBeach #Thailand',
            'expected': 'Thailand',
            'description': 'Thailand in hashtag'
        },
        {
            'text': 'To appreciate the beauty of your surroundings you need to live in the moment.⁣\n\n#ThisIsLiving⁣\n\n📸: @travelifejournal⁣\n\n#Maldives',
            'expected': 'Maldives',
            'description': 'Maldives in hashtag'
        },
        {
            'text': 'The serenity of the ocean never fails to brings a sense of peace.⁣\n\n#ThisIsLiving⁣\n\n📸: @thecapturingcollective⁣\n\n#Bahamas',
            'expected': 'Bahamas',
            'description': 'Bahamas in hashtag'
        },
        {
            'text': 'Take a moment to disconnect and enjoy the tranquility of nature⁣\n#ThisIsLiving⁣\n\n📸: @detojan⁣\n\n#Indonesia',
            'expected': 'Indonesia',
            'description': 'Indonesia in hashtag'
        },
        {
            'text': 'Spend time in nature and feel life slow down.⁣\n\n#ThisIsLiving⁣\n\n📸 @mistertravel.k⁣\n\n#Mexico',
            'expected': 'Mexico',
            'description': 'Mexico in hashtag'
        },
        {
            'text': 'Nothing soothes the soul like a day spent by the water.⁣\n\n#ThisIsLiving⁣\n\n📸: @merry_amber⁣\n\n#Indonesia #MentawaiIslands',
            'expected': 'Indonesia',
            'description': 'Indonesia with location hashtag'
        },
        {
            'text': 'Nature always comes with a view.⁣\n\n#ThisIsLiving⁣\n\n📸: @andreavetrano⁣\n\n#Mykonos #Greece',
            'expected': 'Greece',
            'description': 'Greece with city hashtag'
        }
    ]
    
    print("🧪 TESTING EXTRACTION FIXES")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['description']}")
        print(f"Text: {test_case['text'][:80]}...")
        
        # Test keyword extraction (our primary fix)
        result = validate_country_with_keywords(test_case['text'], COUNTRY_KEYWORD_MAP)
        
        if result == test_case['expected']:
            print(f"✅ PASSED: Extracted '{result}' (expected '{test_case['expected']}')")
            passed += 1
        else:
            print(f"❌ FAILED: Extracted '{result}' (expected '{test_case['expected']}')")
            failed += 1
            
            # Try nationality check as fallback
            nationality_result = check_nationality_references(test_case['text'])
            if nationality_result == test_case['expected']:
                print(f"   ✅ Nationality check would catch this: '{nationality_result}'")
    
    print(f"\n📊 TEST RESULTS:")
    print(f"Passed: {passed}/{len(test_cases)} ({passed/len(test_cases)*100:.1f}%)")
    print(f"Failed: {failed}/{len(test_cases)} ({failed/len(test_cases)*100:.1f}%)")
    
    return passed, failed

def test_regex_fix():
    """Test that the regex fix works correctly"""
    print(f"\n🔧 TESTING REGEX FIX")
    print("=" * 30)
    
    test_text = "I love visiting #Indonesia and #Thailand for vacation"
    
    # Test the old broken regex (double backslash)
    old_pattern = r'\\b' + re.escape('indonesia') + r'\\b'
    old_match = re.search(old_pattern, test_text.lower())
    
    # Test the new fixed regex (single backslash)
    new_pattern = r'\b' + re.escape('indonesia') + r'\b'
    new_match = re.search(new_pattern, test_text.lower())
    
    print(f"Test text: {test_text}")
    print(f"Old regex (broken): {old_pattern} -> {'Found' if old_match else 'Not found'}")
    print(f"New regex (fixed): {new_pattern} -> {'Found' if new_match else 'Not found'}")
    
    if not old_match and new_match:
        print("✅ Regex fix is working correctly!")
        return True
    else:
        print("❌ Regex fix may not be working as expected")
        return False

def main():
    """Main test function"""
    print("🔍 TESTING COUNTRY EXTRACTION FIXES")
    print("=" * 60)
    
    # Test regex fix
    regex_ok = test_regex_fix()
    
    # Test problematic cases
    passed, failed = test_problematic_cases()
    
    print(f"\n🎯 OVERALL RESULTS:")
    print(f"Regex fix: {'✅ Working' if regex_ok else '❌ Issues'}")
    print(f"Test cases: {passed} passed, {failed} failed")
    
    if passed > failed and regex_ok:
        print(f"\n🎉 FIXES ARE WORKING! The extraction should be much more accurate now.")
        print(f"📈 Expected improvement: ~{passed/8*100:.0f}% accuracy on hashtag cases")
    else:
        print(f"\n⚠️  Some issues remain. May need additional refinement.")

if __name__ == "__main__":
    main()
