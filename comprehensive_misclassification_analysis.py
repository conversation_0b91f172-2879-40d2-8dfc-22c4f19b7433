#!/usr/bin/env python3
"""
Comprehensive analysis to identify additional country extraction misclassifications
beyond hashtag issues. This script analyzes patterns, categorizes errors, and
identifies systematic problems in the extraction logic.
"""

import pandas as pd
import re
import pycountry
from collections import defaultdict, Counter
import random
from geopy.geocoders import Nominatim
import time

class CountryExtractionAnalyzer:
    def __init__(self):
        self.geolocator = Nominatim(user_agent="country_analyzer/1.0")
        self.error_categories = {
            'geographic_mismatch': [],
            'city_mapping_error': [],
            'cultural_reference_failure': [],
            'brand_confusion': [],
            'multiple_country_issue': [],
            'no_apparent_connection': [],
            'regional_reference_error': []
        }
        
        # Known cities and their countries for validation
        self.city_country_map = {
            'london': 'United Kingdom',
            'paris': 'France',
            'tokyo': 'Japan',
            'new york': 'United States',
            'sydney': 'Australia',
            'rome': 'Italy',
            'berlin': 'Germany',
            'madrid': 'Spain',
            'amsterdam': 'Netherlands',
            'vienna': 'Austria',
            'zurich': 'Switzerland',
            'stockholm': 'Sweden',
            'oslo': 'Norway',
            'copenhagen': 'Denmark',
            'helsinki': 'Finland',
            'dublin': 'Ireland',
            'lisbon': 'Portugal',
            'athens': 'Greece',
            'prague': 'Czech Republic',
            'budapest': 'Hungary',
            'warsaw': 'Poland',
            'moscow': 'Russia',
            'beijing': 'China',
            'mumbai': 'India',
            'delhi': 'India',
            'bangkok': 'Thailand',
            'singapore': 'Singapore',
            'hong kong': 'China',
            'dubai': 'United Arab Emirates',
            'cairo': 'Egypt',
            'cape town': 'South Africa',
            'lagos': 'Nigeria',
            'nairobi': 'Kenya',
            'rio de janeiro': 'Brazil',
            'buenos aires': 'Argentina',
            'mexico city': 'Mexico',
            'toronto': 'Canada',
            'vancouver': 'Canada',
            'montreal': 'Canada',
            'los angeles': 'United States',
            'chicago': 'United States',
            'miami': 'United States',
            'san francisco': 'United States',
            'las vegas': 'United States',
            'bali': 'Indonesia',
            'phuket': 'Thailand',
            'mykonos': 'Greece',
            'santorini': 'Greece',
            'ibiza': 'Spain',
            'mallorca': 'Spain',
            'cannes': 'France',
            'nice': 'France',
            'monaco': 'Monaco',
            'venice': 'Italy',
            'florence': 'Italy',
            'milan': 'Italy',
            'barcelona': 'Spain',
            'seville': 'Spain',
            'porto': 'Portugal',
            'reykjavik': 'Iceland',
            'marrakech': 'Morocco',
            'casablanca': 'Morocco',
            'tunis': 'Tunisia',
            'male': 'Maldives',
            'nassau': 'Bahamas',
            'havana': 'Cuba',
            'kingston': 'Jamaica',
            'bridgetown': 'Barbados',
            'punta cana': 'Dominican Republic',
            'cancun': 'Mexico',
            'tulum': 'Mexico',
            'playa del carmen': 'Mexico',
            'costa rica': 'Costa Rica',
            'san jose': 'Costa Rica',
            'panama city': 'Panama',
            'cartagena': 'Colombia',
            'bogota': 'Colombia',
            'medellin': 'Colombia',
            'quito': 'Ecuador',
            'lima': 'Peru',
            'cusco': 'Peru',
            'la paz': 'Bolivia',
            'santiago': 'Chile',
            'montevideo': 'Uruguay',
            'asuncion': 'Paraguay'
        }
        
        # Known brands that might be confused with places
        self.brand_names = {
            'corona', 'amazon', 'apple', 'google', 'microsoft', 'facebook', 
            'instagram', 'twitter', 'nike', 'adidas', 'coca cola', 'pepsi',
            'starbucks', 'mcdonalds', 'burger king', 'kfc', 'pizza hut',
            'uber', 'airbnb', 'booking', 'expedia', 'tripadvisor'
        }
        
        # Cultural/language indicators
        self.cultural_indicators = {
            'cuisine': ['sushi', 'pasta', 'pizza', 'tacos', 'curry', 'paella', 'croissant'],
            'language': ['english', 'spanish', 'french', 'german', 'italian', 'portuguese', 'chinese', 'japanese'],
            'architecture': ['gothic', 'baroque', 'renaissance', 'colonial', 'modern'],
            'festivals': ['carnival', 'oktoberfest', 'diwali', 'chinese new year', 'ramadan']
        }

    def load_dataset(self, file_path):
        """Load the merged dataset"""
        try:
            df = pd.read_excel(file_path)
            print(f"✓ Successfully loaded dataset: {df.shape}")
            return df
        except Exception as e:
            print(f"❌ Error loading dataset: {e}")
            return None

    def extract_text_content(self, row):
        """Extract and combine text content from a row"""
        title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
        description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
        
        if title and description:
            return f"{title}. {description}"
        elif title:
            return title
        elif description:
            return description
        else:
            return ""

    def find_cities_in_text(self, text):
        """Find city mentions in text"""
        if not text:
            return []
        
        text_lower = text.lower()
        found_cities = []
        
        for city, country in self.city_country_map.items():
            if re.search(r'\b' + re.escape(city) + r'\b', text_lower):
                found_cities.append((city, country))
        
        return found_cities

    def find_countries_in_text(self, text):
        """Find country mentions in text using multiple methods"""
        if not text:
            return []
        
        text_lower = text.lower()
        found_countries = []
        
        # Check pycountry database
        try:
            for country in pycountry.countries:
                if country.name.lower() in text_lower:
                    found_countries.append(country.name)
                if hasattr(country, 'common_name') and country.common_name.lower() in text_lower:
                    found_countries.append(country.common_name)
        except:
            pass
        
        # Check common country variations
        common_countries = {
            'usa': 'United States', 'america': 'United States', 'us': 'United States',
            'uk': 'United Kingdom', 'britain': 'United Kingdom', 'england': 'United Kingdom',
            'uae': 'United Arab Emirates', 'emirates': 'United Arab Emirates',
            'korea': 'South Korea', 'holland': 'Netherlands'
        }
        
        for variant, country in common_countries.items():
            if re.search(r'\b' + re.escape(variant) + r'\b', text_lower):
                found_countries.append(country)
        
        return list(set(found_countries))

    def check_geographic_consistency(self, extracted_country, text_countries, text_cities):
        """Check if extracted country is geographically consistent with text content"""
        if not extracted_country or extracted_country == 'N/A':
            return True, "No extraction to validate"
        
        # Check direct country matches
        if extracted_country in text_countries:
            return True, "Direct country match"
        
        # Check city-country consistency
        for city, expected_country in text_cities:
            if extracted_country == expected_country:
                return True, f"Consistent with city {city}"
        
        # Check if extracted country is in same region as mentioned countries/cities
        all_mentioned_countries = text_countries + [country for _, country in text_cities]
        if all_mentioned_countries:
            # Simple continent-level check (could be enhanced)
            return False, f"Geographic mismatch: extracted {extracted_country}, found {all_mentioned_countries}"
        
        return False, "No geographic references found in text"

    def analyze_sample(self, df, sample_size=200):
        """Analyze a sample of the dataset for misclassifications"""
        print(f"\n🔍 ANALYZING SAMPLE OF {sample_size} ROWS")
        print("=" * 50)
        
        # Take a stratified sample to ensure variety
        sample_df = df.sample(n=min(sample_size, len(df)), random_state=42)
        
        misclassifications = []
        
        for idx, row in sample_df.iterrows():
            text = self.extract_text_content(row)
            if not text.strip():
                continue
            
            extracted_country = row.get('Extracted Country', 'N/A')
            
            # Find geographic references in text
            text_countries = self.find_countries_in_text(text)
            text_cities = self.find_cities_in_text(text)
            
            # Check for consistency
            is_consistent, reason = self.check_geographic_consistency(
                extracted_country, text_countries, text_cities
            )
            
            if not is_consistent:
                error_type = self.categorize_error(
                    text, extracted_country, text_countries, text_cities, reason
                )
                
                misclassification = {
                    'row_idx': idx,
                    'text': text[:200] + "..." if len(text) > 200 else text,
                    'extracted': extracted_country,
                    'text_countries': text_countries,
                    'text_cities': text_cities,
                    'error_type': error_type,
                    'reason': reason
                }
                
                misclassifications.append(misclassification)
                self.error_categories[error_type].append(misclassification)
        
        return misclassifications

    def categorize_error(self, text, extracted_country, text_countries, text_cities, reason):
        """Categorize the type of extraction error"""
        text_lower = text.lower()
        
        # Check for brand confusion
        for brand in self.brand_names:
            if brand in text_lower and brand != 'corona':  # Corona is legitimate in this context
                return 'brand_confusion'
        
        # Check for multiple country mentions
        if len(text_countries) > 1:
            return 'multiple_country_issue'
        
        # Check for city mapping errors
        if text_cities and not text_countries:
            return 'city_mapping_error'
        
        # Check for cultural reference failures
        for category, indicators in self.cultural_indicators.items():
            for indicator in indicators:
                if indicator in text_lower:
                    return 'cultural_reference_failure'
        
        # Check for regional references
        regional_terms = ['europe', 'asia', 'africa', 'america', 'oceania', 'caribbean', 'mediterranean', 'scandinavia']
        for term in regional_terms:
            if term in text_lower:
                return 'regional_reference_error'
        
        # Check for geographic mismatches
        if text_countries or text_cities:
            return 'geographic_mismatch'
        
        # Default: no apparent connection
        return 'no_apparent_connection'

    def analyze_error_patterns(self, misclassifications):
        """Analyze patterns in the misclassifications"""
        print(f"\n📊 ERROR PATTERN ANALYSIS")
        print("=" * 40)
        
        # Count by error type
        error_counts = Counter([m['error_type'] for m in misclassifications])
        
        print(f"Total misclassifications found: {len(misclassifications)}")
        print(f"\nError types:")
        for error_type, count in error_counts.most_common():
            percentage = (count / len(misclassifications)) * 100
            print(f"  {error_type}: {count} ({percentage:.1f}%)")
        
        # Analyze most common incorrect extractions
        incorrect_extractions = Counter([m['extracted'] for m in misclassifications])
        print(f"\nMost frequently incorrect extractions:")
        for country, count in incorrect_extractions.most_common(10):
            print(f"  {country}: {count} times")
        
        return error_counts

    def show_examples_by_category(self, max_examples=3):
        """Show examples of each error category"""
        print(f"\n📋 EXAMPLES BY ERROR CATEGORY")
        print("=" * 50)
        
        for category, errors in self.error_categories.items():
            if not errors:
                continue
                
            print(f"\n🔸 {category.upper().replace('_', ' ')} ({len(errors)} cases)")
            print("-" * 40)
            
            for i, error in enumerate(errors[:max_examples]):
                print(f"\nExample {i+1} (Row {error['row_idx']}):")
                print(f"  Text: {error['text']}")
                print(f"  Extracted: {error['extracted']}")
                print(f"  Found in text: Countries={error['text_countries']}, Cities={error['text_cities']}")
                print(f"  Reason: {error['reason']}")

def main():
    """Main analysis function"""
    print("🔍 COMPREHENSIVE COUNTRY EXTRACTION MISCLASSIFICATION ANALYSIS")
    print("=" * 70)
    
    analyzer = CountryExtractionAnalyzer()
    
    # Load dataset
    df = analyzer.load_dataset("data/complete_merged_data_20250526_175235.xlsx")
    if df is None:
        return
    
    # Analyze a larger sample
    misclassifications = analyzer.analyze_sample(df, sample_size=200)
    
    # Analyze patterns
    error_counts = analyzer.analyze_error_patterns(misclassifications)
    
    # Show examples
    analyzer.show_examples_by_category(max_examples=2)
    
    # Summary and recommendations
    print(f"\n🎯 SUMMARY AND NEXT STEPS")
    print("=" * 40)
    
    total_sample = 200
    accuracy_rate = ((total_sample - len(misclassifications)) / total_sample) * 100
    
    print(f"Sample accuracy: {accuracy_rate:.1f}%")
    print(f"Misclassification rate: {(len(misclassifications) / total_sample) * 100:.1f}%")
    
    if error_counts:
        top_error = error_counts.most_common(1)[0]
        print(f"Primary issue: {top_error[0]} ({top_error[1]} cases)")
    
    print(f"\n💡 RECOMMENDED FIXES:")
    print("1. Expand city-to-country mapping")
    print("2. Improve multiple country handling")
    print("3. Add brand name filtering")
    print("4. Enhance cultural reference detection")
    print("5. Implement geographic consistency validation")

if __name__ == "__main__":
    main()
