#!/usr/bin/env python3
"""
Detailed analysis of the specific misclassification patterns to create targeted fixes.
This script focuses on understanding WHY the extractions are wrong and what can be fixed.
"""

import pandas as pd
import re
import pycountry
from collections import defaultdict, Counter

def analyze_specific_issues():
    """Analyze specific problematic patterns in detail"""
    
    print("🔍 DETAILED ERROR ANALYSIS")
    print("=" * 50)
    
    # Load dataset
    try:
        df = pd.read_excel("data/complete_merged_data_20250526_175235.xlsx")
        print(f"✓ Loaded dataset: {df.shape}")
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    # Analyze the most problematic extractions
    problematic_countries = ['United Kingdom', 'France', 'Lithuania', 'Chad', 'Comoros', 'Dominican Republic', 'Eritrea']
    
    print(f"\n🎯 ANALYZING MOST PROBLEMATIC EXTRACTIONS")
    print("-" * 50)
    
    for country in problematic_countries[:3]:  # Focus on top 3
        print(f"\n🔸 ANALYZING '{country}' EXTRACTIONS")
        country_rows = df[df['Extracted Country'] == country]
        
        print(f"Total '{country}' extractions: {len(country_rows)}")
        
        # Sample a few cases
        sample_size = min(5, len(country_rows))
        sample_rows = country_rows.sample(n=sample_size, random_state=42)
        
        for idx, row in sample_rows.iterrows():
            title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
            description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
            
            combined_text = ""
            if title and description:
                combined_text = f"{title}. {description}"
            elif title:
                combined_text = title
            elif description:
                combined_text = description
            
            print(f"\n  Row {idx}:")
            print(f"    Text: {combined_text[:150]}...")
            print(f"    Post type: {row.get('Post type', 'N/A')}")
            print(f"    Source: {str(row.get('source_file_path', 'N/A'))[-50:]}")
            
            # Check for obvious geographic references
            text_lower = combined_text.lower()
            obvious_countries = []
            
            # Check for country names
            for pycountry_country in pycountry.countries:
                if pycountry_country.name.lower() in text_lower:
                    obvious_countries.append(pycountry_country.name)
            
            # Check for common variations
            common_refs = {
                'usa': 'United States', 'america': 'United States',
                'uk': 'United Kingdom', 'britain': 'United Kingdom',
                'uae': 'United Arab Emirates'
            }
            
            for ref, country_name in common_refs.items():
                if ref in text_lower:
                    obvious_countries.append(country_name)
            
            if obvious_countries:
                print(f"    ⚠️  Found countries in text: {obvious_countries}")
            else:
                print(f"    ℹ️  No obvious country references found")

def analyze_corona_island_confusion():
    """Analyze the Corona Island -> Colombia mapping issue"""
    
    print(f"\n🏝️  CORONA ISLAND ANALYSIS")
    print("-" * 40)
    
    try:
        df = pd.read_excel("data/complete_merged_data_20250526_175235.xlsx")
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    # Find posts mentioning "Corona Island"
    corona_island_posts = []
    
    for idx, row in df.iterrows():
        title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
        description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
        
        combined_text = f"{title}. {description}".lower()
        
        if 'corona island' in combined_text:
            corona_island_posts.append({
                'idx': idx,
                'text': combined_text[:200],
                'extracted': row.get('Extracted Country', 'N/A')
            })
    
    print(f"Found {len(corona_island_posts)} posts mentioning 'Corona Island'")
    
    for post in corona_island_posts[:5]:
        print(f"\nRow {post['idx']}:")
        print(f"  Text: {post['text']}...")
        print(f"  Extracted: {post['extracted']}")

def analyze_generic_posts():
    """Analyze posts with no geographic content that still get country extractions"""
    
    print(f"\n🌐 GENERIC POST ANALYSIS")
    print("-" * 40)
    
    try:
        df = pd.read_excel("data/complete_merged_data_20250526_175235.xlsx")
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    # Look for very generic posts that shouldn't have country extractions
    generic_patterns = [
        r'^[^.]*beer[^.]*$',
        r'^[^.]*sunset[^.]*$',
        r'^[^.]*relax[^.]*$',
        r'^[^.]*nature[^.]*$',
        r'^[^.]*moment[^.]*$'
    ]
    
    generic_posts = []
    
    for idx, row in df.iterrows():
        title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
        description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
        
        combined_text = f"{title}. {description}".lower()
        extracted = row.get('Extracted Country', 'N/A')
        
        # Skip if no extraction or N/A
        if not extracted or extracted == 'N/A':
            continue
        
        # Check if text is very generic (short and no obvious geographic references)
        if len(combined_text.strip()) < 100:  # Short posts
            # Check for obvious geographic terms
            geographic_terms = ['#', 'city', 'country', 'island', 'beach', 'mountain', 'river', 'ocean']
            has_geographic = any(term in combined_text for term in geographic_terms)
            
            if not has_geographic:
                generic_posts.append({
                    'idx': idx,
                    'text': combined_text[:150],
                    'extracted': extracted,
                    'length': len(combined_text)
                })
    
    print(f"Found {len(generic_posts)} potentially generic posts with extractions")
    
    # Show examples
    for post in generic_posts[:5]:
        print(f"\nRow {post['idx']} (length: {post['length']}):")
        print(f"  Text: {post['text']}")
        print(f"  Extracted: {post['extracted']}")

def identify_missing_city_mappings():
    """Identify cities mentioned in text that aren't in our mapping"""
    
    print(f"\n🏙️  MISSING CITY MAPPING ANALYSIS")
    print("-" * 40)
    
    try:
        df = pd.read_excel("data/complete_merged_data_20250526_175235.xlsx")
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    # Common city patterns to look for
    city_patterns = [
        r'#([A-Z][a-z]+(?:[A-Z][a-z]+)*)',  # Hashtag cities like #CapeTown
        r'\b([A-Z][a-z]+ [A-Z][a-z]+)\b',   # Two-word cities like "Cape Town"
        r'\b([A-Z][a-z]+)\b(?=\s+(?:beach|island|bay|coast))',  # Cities before geographic terms
    ]
    
    potential_cities = Counter()
    
    sample_size = 100
    sample_df = df.sample(n=sample_size, random_state=42)
    
    for idx, row in sample_df.iterrows():
        title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
        description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
        
        combined_text = f"{title}. {description}"
        
        for pattern in city_patterns:
            matches = re.findall(pattern, combined_text)
            for match in matches:
                if len(match) > 3 and match.lower() not in ['this', 'that', 'with', 'from', 'your', 'life']:
                    potential_cities[match] += 1
    
    print(f"Potential cities found in sample:")
    for city, count in potential_cities.most_common(15):
        print(f"  {city}: {count} mentions")

def main():
    """Main analysis function"""
    print("🔍 DETAILED MISCLASSIFICATION ERROR ANALYSIS")
    print("=" * 60)
    
    # Analyze specific problematic countries
    analyze_specific_issues()
    
    # Analyze Corona Island confusion
    analyze_corona_island_confusion()
    
    # Analyze generic posts
    analyze_generic_posts()
    
    # Identify missing city mappings
    identify_missing_city_mappings()
    
    print(f"\n🎯 KEY FINDINGS SUMMARY")
    print("=" * 40)
    print("1. Many extractions appear to be random/incorrect")
    print("2. 'Corona Island' is being mapped to Colombia incorrectly")
    print("3. Generic posts without geographic content get country extractions")
    print("4. Missing city-to-country mappings for many locations")
    print("5. Gemini API may be hallucinating countries for non-geographic content")
    
    print(f"\n💡 PRIORITY FIXES NEEDED:")
    print("1. 🚨 HIGH: Fix Corona Island mapping (it's a fictional/branded location)")
    print("2. 🚨 HIGH: Add validation to reject extractions for generic content")
    print("3. 🔧 MED: Expand city-to-country mapping")
    print("4. 🔧 MED: Improve Gemini prompt to be more conservative")
    print("5. 🔧 LOW: Add geographic consistency validation")

if __name__ == "__main__":
    main()
