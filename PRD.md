# Product Requirements Document (PRD) Template

---

## 1. Project Overview
- **Project Name:** - Country Extraction Tool

---

## 2. Problem Statement
- What problem are we solving? - We have some excel files that have comment descriptions (captions of posts), I want to extract any specified country or location in a country and add a coulmn with corresponding country in it
- Why is this important? - Helps locate where the post is referring to

---

## 3. Goals & Objectives
- What are the primary goals of this project? - Identifying the location in the text and finding corresponding country from it using LLM
- What does success look like? - A new column in the excel - that lists corresponding country based on the "description column"

---

## 4. Target Users / Personas

- User personas (describe typical users, their needs, pain points) - a non coder that needs a way to update any excel so that it can work on different datasets.

---

## 5. User Stories / Use Cases
- List user stories or use cases (e.g., "As a [user], I want to [do something] so that [reason]") - As a user, i want a tool (can be a web app or a GUI Script) that can take an excel, read the description column that i identify and go through it, find a location mention or  a country mention, add the corresponding country - even if it is a place i need the country it is in so that i can tag it to that country in my  database.

---

## 6. Features & Requirements
### 6.1 Functional Requirements
- List all features and their descriptions - Identifying the location in the text and finding corresponding country from it using LLM,  even if it is a place i need the country it is in so that i can tag it to that country in my  database.
- Prioritize (Must, Should, Could, Won't)
 - Must - Find the country using LLM (gemini free api) from the description
 - Should - handle multiple countries if any 
 - could - provide a graphical interface to run the program on a new set of data .
 - could - the user can select the description column manually

---

## 7. Technical Details
### 7.1 Architecture Overview
- A Python script will serve as the core of the tool.
- The script will take an Excel file path and the name of the description column as input.
- It will read the specified column from the Excel file.
- For each entry in the description column, it will query the Gemini LLM API to identify locations and their corresponding countries.
- The script will then write the results (extracted countries) into a new column in the original Excel file or a new output file.
- If a GUI is developed (a "could have" feature), it will wrap this Python script, providing a user-friendly interface for file selection and column specification.

### 7.2 Tech Stack
- **Programming Language:** Python
- **Libraries:**
    - `pandas`: For reading and writing Excel files.
    - `google-generativeai` (or similar): For interacting with the Gemini API.
    - `tkinter` / `PyQt` / `Streamlit` / `Gradio` (Optional, if GUI is pursued): For the graphical user interface.
- **Tools:** A code editor (e.g., VS Code), Git (for version control, if applicable).

### 7.3 APIs / Integrations
- **External APIs:**
    - Google Gemini API: For natural language processing to extract location and country information.
- **Integration Points:**
    - The script integrates with the local file system to read input Excel files and write output.
    - Direct API calls to the Gemini service.

### 7.4 Data Model
- **Input:**
    - An Excel file (`.xlsx` or `.xls`).
    - User-specified column name containing text descriptions.
- **Processing:**
    - Each row's description text is processed individually.
- **Output:**
    - The original Excel file is modified to include a new column (e.g., "Extracted Country").
    - This new column will contain the country name(s) identified from the corresponding description. If multiple countries are found, they could be comma-separated or handled as per the "Should" requirement. If no country is found, the cell might be empty or have a placeholder like "N/A".
- **Key Entities:**
    - `ExcelSheet`: Represents the input and output data.
    - `DescriptionText`: The input text from which to extract information.
    - `Location`: An identified place name within the description.
    - `Country`: The country corresponding to the identified location.

---

## 8. Success Metrics & KPIs
- **How will we measure success?**
    - By the tool's ability to accurately and efficiently extract country information and by user satisfaction (especially for non-coders).
- **Quantitative and qualitative metrics:**
    - **Accuracy Rate:** Percentage of descriptions where the country is correctly identified (e.g., >90% on a test dataset). This can be measured by comparing tool output against a manually verified dataset.
    - **Processing Speed:** Time taken to process an Excel file (e.g., average time per 100 rows).
    - **Error Rate:** Percentage of descriptions where the tool fails to process or returns an incorrect country.
    - **Coverage:** Percentage of descriptions for which a country (or "N/A") is returned.
    - **User Feedback (Qualitative):** For non-coder users, ease of use, clarity of instructions, and overall satisfaction with the tool's output. This is more relevant if a GUI is implemented.
    - **LLM API Usage:** Monitoring the number of API calls to stay within free tier limits or manage costs.

---

## 9. Acceptance Criteria
- **What must be true for this project to be considered complete?**
    - The tool can successfully read a user-specified column from an input Excel file.
    - The tool correctly queries the Gemini LLM API with the text from the description column.
    - The tool accurately extracts country names from the descriptions. If a city or specific place is mentioned, the tool should return the country that place is in.
    - A new column (e.g., "Extracted Country") is added to the Excel file.
    - This new column is populated with the identified country names.
    - The tool handles cases where no location/country is mentioned in the description (e.g., leaves the cell blank or adds "N/A").
    - The tool can handle multiple country mentions in a single description field as per the "Should" requirement (e.g., comma-separated list or primary country).
    - The core functionality (country extraction and Excel update) works reliably for a sample set of Excel files provided by the user.
    - Basic error handling is in place (e.g., file not found, invalid column name, API errors).
- **Test cases, demo scenarios:**
    - **Scenario 1:** Excel file with clear country mentions (e.g., "Vacation in France"). Expected: "France" in the new column.
    - **Scenario 2:** Excel file with city/region mentions (e.g., "Visited Paris last summer"). Expected: "France".
    - **Scenario 3:** Excel file with descriptions containing no locations. Expected: Blank or "N/A".
    - **Scenario 4:** Excel file with multiple locations/countries (e.g., "Trip from London, UK to Berlin, Germany"). Expected: "United Kingdom, Germany" or similar based on handling strategy.
    - **Scenario 5:** Excel file with ambiguous or less common locations. Performance to be noted.
    - **Scenario 6 (GUI - if implemented):** User can easily select an Excel file and specify the description column through the interface.

---

## 10. Edge Cases & Risks
- **What are the known edge cases?**
    - **Ambiguous Locations:** Places with the same name in different countries (e.g., Paris, Texas vs. Paris, France). The LLM's contextual understanding will be key.
    - **Colloquial/Informal Place Names:** Slang or unofficial names for locations.
    - **Historical Place Names:** Locations whose names or country affiliations have changed.
    - **Fictional Locations:** Descriptions mentioning places that do not exist.
    - **Multiple Languages:** Descriptions containing location names in languages other than English (if the LLM is primarily tuned for English).
    - **No Geographical Context:** Descriptions that are purely abstract or do not refer to any place.
    - **Very Long Descriptions:** Potential impact on LLM processing time or token limits.
    - **Formatting Issues in Excel:** Merged cells, special characters in column names or data.
- **What risks or dependencies exist?**
    - **LLM Accuracy:** The accuracy of the Gemini API is crucial. Misinterpretations can lead to incorrect data.
    - **LLM API Availability/Changes:** Dependency on the Gemini API being available and its terms of service (especially free tier limitations, rate limits, or potential future costs).
    - **Internet Connectivity:** Required for LLM API calls.
    - **Data Privacy:** Sending description data to an external LLM. Ensure compliance with any privacy policies if data is sensitive (though for post captions, this might be less of a concern).
    - **Scalability for Large Files:** Performance might degrade with very large Excel files.
    - **Changes in Excel File Structure:** If users provide files with unexpected structures.
- **Mitigation strategies:**
    - **LLM Prompt Engineering:** Carefully craft prompts for the Gemini API to improve accuracy and handle ambiguity.
    - **Confidence Scoring (if available from LLM):** Potentially flag low-confidence extractions for manual review.
    - **User Validation/Correction:** Allow users to review and correct the extracted countries, especially for critical applications.
    - **Batch Processing & Retries:** For API calls, implement retries for transient errors and process data in batches to manage rate limits.
    - **Clear Error Reporting:** Provide informative error messages to the user.
    - **User Documentation/Guidelines:** Instruct users on expected input format and limitations.
    - **Local Fallback (Limited):** For very common, unambiguous country names, a simple local lookup could be a (very limited) fallback if the API is unavailable, but this is likely out of scope for the initial version.
    - **Configuration for API Key:** Allow users to input their own Gemini API key if needed.

---

## 11. Out of Scope
- **What is explicitly not included in this project?**
    - **Real-time processing or continuous monitoring of files.** The tool will be run on demand.
    - **Support for file formats other than Excel (`.xlsx`, `.xls`).**
    - **Advanced Natural Language Understanding:** Beyond identifying locations and their countries (e.g., sentiment, intent, named entities other than locations).
    - **Complex GUI features:** The initial "must" is a working script. GUI is a "could have" and would be basic if implemented.
    - **User authentication or multi-user management.**
    - **Offline functionality for country extraction** (due to reliance on LLM API).
    - **Automatic language detection and translation** for descriptions in multiple languages (unless the chosen LLM handles this transparently).
    - **Detailed GIS mapping or distance calculations.**
    - **Version control or history tracking of changes within the tool itself.**

---

## 12. Timeline & Milestones
-need only one working version in two days.

---

## 13. Appendix / References
- **Google Gemini API Documentation:** [Link to be added, e.g., https://ai.google.dev/docs/gemini_api_overview]
- **Pandas Documentation:** [Link to be added, e.g., https://pandas.pydata.org/pandas-docs/stable/]
- **Python `google-generativeai` library:** [Link to be added, e.g., https://pypi.org/project/google-generativeai/]

---
