#!/usr/bin/env python3
"""
Script to merge January 2025 data files into the existing merged dataset.
This script handles different column structures and creates a unified dataset.
"""

import pandas as pd
import os
from datetime import datetime

def read_excel_file(file_path):
    """Read an Excel file and return the DataFrame"""
    try:
        df = pd.read_excel(file_path)
        print(f"✓ Successfully read {file_path}")
        print(f"  - Shape: {df.shape}")
        print(f"  - Columns: {len(df.columns)}")
        return df
    except Exception as e:
        print(f"✗ Error reading {file_path}: {e}")
        return None

def get_unified_columns(dataframes):
    """Get all unique columns from all dataframes"""
    all_columns = set()
    for df in dataframes:
        if df is not None:
            all_columns.update(df.columns)
    return sorted(list(all_columns))

def standardize_dataframe(df, unified_columns, source_file):
    """Standardize a dataframe to have all unified columns"""
    if df is None:
        return None
    
    # Add source file information
    df = df.copy()
    if 'source_file_path' not in df.columns:
        df['source_file_path'] = source_file
    
    # Add missing columns with NaN values
    for col in unified_columns:
        if col not in df.columns:
            df[col] = pd.NA
    
    # Reorder columns to match unified structure
    df = df[unified_columns]
    
    return df

def merge_data_files():
    """Main function to merge all data files"""
    
    # Define file paths
    data_dir = "data"
    files = {
        "existing_merged": os.path.join(data_dir, "merged_output_with_countries_with_countries.xlsx"),
        "jan_fb": os.path.join(data_dir, "Jan-01-2025_Mar-31-2025_FB_with_countries.xlsx"),
        "jan_ig": os.path.join(data_dir, "Jan-01-2025_Mar-31-2025_IG_with_countries.xlsx")
    }
    
    print("🔄 Starting data merge process...")
    print("=" * 50)
    
    # Read all files
    dataframes = {}
    for name, file_path in files.items():
        if os.path.exists(file_path):
            dataframes[name] = read_excel_file(file_path)
        else:
            print(f"⚠️  File not found: {file_path}")
            dataframes[name] = None
    
    # Filter out None dataframes
    valid_dataframes = {k: v for k, v in dataframes.items() if v is not None}
    
    if not valid_dataframes:
        print("❌ No valid files found to merge!")
        return
    
    print(f"\n📊 Found {len(valid_dataframes)} valid files to merge")
    
    # Get unified column structure
    unified_columns = get_unified_columns(valid_dataframes.values())
    print(f"📋 Total unique columns across all files: {len(unified_columns)}")
    
    # Standardize all dataframes
    standardized_dfs = []
    for name, df in valid_dataframes.items():
        print(f"\n🔧 Standardizing {name}...")
        standardized_df = standardize_dataframe(df, unified_columns, files[name])
        if standardized_df is not None:
            standardized_dfs.append(standardized_df)
            print(f"  ✓ Standardized to {standardized_df.shape}")
    
    # Merge all dataframes
    print(f"\n🔗 Merging {len(standardized_dfs)} dataframes...")
    merged_df = pd.concat(standardized_dfs, ignore_index=True)
    
    print(f"✓ Merged dataset shape: {merged_df.shape}")
    
    # Generate output filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(data_dir, f"complete_merged_data_{timestamp}.xlsx")
    
    # Save the merged data
    print(f"\n💾 Saving merged data to: {output_file}")
    try:
        merged_df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"✅ Successfully saved merged data!")
        print(f"📈 Final dataset contains {len(merged_df)} rows and {len(merged_df.columns)} columns")
        
        # Show some statistics
        print(f"\n📊 Data Summary:")
        print(f"  - Total posts: {len(merged_df)}")
        if 'Post type' in merged_df.columns:
            print(f"  - Post types: {merged_df['Post type'].value_counts().to_dict()}")
        if 'Extracted Country' in merged_df.columns:
            country_counts = merged_df['Extracted Country'].value_counts().head(10)
            print(f"  - Top countries: {country_counts.to_dict()}")
        
        return output_file
        
    except Exception as e:
        print(f"❌ Error saving file: {e}")
        return None

if __name__ == "__main__":
    result = merge_data_files()
    if result:
        print(f"\n🎉 Merge completed successfully!")
        print(f"📁 Output file: {result}")
    else:
        print(f"\n❌ Merge failed!")
