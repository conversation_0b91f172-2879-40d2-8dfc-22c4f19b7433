
def extract_country_from_text(text):
    if pd.isna(text) or not str(text).strip():
        return "N/A"

    original_text_for_validation = str(text) # Keep original for validators

    # Preprocess text to handle edge cases
    preprocessed_text = preprocess_text_for_edge_cases(original_text_for_validation)
    if preprocessed_text is None:
        return "N/A"

    # IMPROVED: Try keyword matching FIRST for hashtag content
    # This catches obvious cases like #Philippines, #Indonesia, etc.
    country_keywords = validate_country_with_keywords(original_text_for_validation, COUNTRY_KEYWORD_MAP)
    if country_keywords != "N/A":
        return country_keywords

    # Enhanced prompt that specifically mentions hashtags
    prompt = (
        "Given the following text, extract the country or countries mentioned. "
        "Pay special attention to hashtags (words starting with #) as they often contain location information. "
        "If a city, region, or place is mentioned, return the country it is in. "
        "If multiple countries are mentioned, return all as a comma-separated list. "
        "Be thorough and look for both explicit and implicit country references. "
        "Consider cities, regions, nationalities, cultural references, and hashtags. "
        "If no country or location is found, return 'N/A'.\n\n"
        f"Text: {preprocessed_text}\nCountries: "
    )
    
    country_gemini = "N/A" # Default
    try:
        model = genai.GenerativeModel(model_name='gemini-2.0-flash')
        generation_config = types.GenerationConfig(
            max_output_tokens=128, # Increased for more detailed responses
            temperature=0.1, # Slight randomness to catch edge cases
        )
        response = model.generate_content(
            contents=prompt,
            generation_config=generation_config
        )
        country_gemini = response.text.strip()

        if not country_gemini or country_gemini.lower() == 'n/a':
            country_gemini = "N/A"

    except core_exceptions.ResourceExhausted as e:
        raise RuntimeError("Rate limited by Gemini API. Please try again later or reduce batch size/delay.")
    except Exception as e:
        st.warning(f"Gemini API error for text '{original_text_for_validation[:50]}...': {e}")
        country_gemini = "ERROR: Gemini API failed"

    # IMPROVED: Validate Gemini's output more thoroughly
    if country_gemini != "N/A" and not country_gemini.startswith("ERROR:"):
        # Validate Gemini's output against known country names
        validated_countries = []
        for country in country_gemini.split(','):
            country = country.strip()
            # Try multiple validation approaches
            if pycountry.countries.get(name=country.title()):
                validated_countries.append(country.title())
            elif country.lower() in COUNTRY_KEYWORD_MAP:
                validated_countries.append(COUNTRY_KEYWORD_MAP[country.lower()])
            # Check if it's a fuzzy match for a known country
            else:
                fuzzy_result = validate_country_with_gazetteer_and_fuzzy_search(country, COUNTRY_KEYWORD_MAP)
                if fuzzy_result != "N/A":
                    validated_countries.append(fuzzy_result)

        if validated_countries:
            return ', '.join(validated_countries)

    # Enhanced Validation Steps - try all methods if Gemini fails or gives invalid results
    if country_gemini == "N/A" or country_gemini.startswith("ERROR:") or not country_gemini:
        
        # Try Geopy next
        country_geopy = validate_country_with_geopy(original_text_for_validation)
        if country_geopy != "N/A":
            return country_geopy

        # Try gazetteer + fuzzy search
        country_gaz_fuzzy = validate_country_with_gazetteer_and_fuzzy_search(original_text_for_validation, COUNTRY_KEYWORD_MAP)
        if country_gaz_fuzzy != "N/A":
            return country_gaz_fuzzy

        # Final attempt: check for nationality words and cultural references
        country_from_nationality = check_nationality_references(original_text_for_validation)
        if country_from_nationality != "N/A":
            return country_from_nationality

        # If Gemini had an error, return that error
        if country_gemini.startswith("ERROR:"):
            return country_gemini

        return "N/A"

    # If we get here, Gemini gave a result but it didn't validate
    # Try fallback methods
    country_geopy = validate_country_with_geopy(original_text_for_validation)
    if country_geopy != "N/A":
        return country_geopy

    country_gaz_fuzzy = validate_country_with_gazetteer_and_fuzzy_search(original_text_for_validation, COUNTRY_KEYWORD_MAP)
    if country_gaz_fuzzy != "N/A":
        return country_gaz_fuzzy

    country_from_nationality = check_nationality_references(original_text_for_validation)
    if country_from_nationality != "N/A":
        return country_from_nationality

    return "N/A"
