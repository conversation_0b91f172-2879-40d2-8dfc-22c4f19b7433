#!/usr/bin/env python3
"""
<PERSON>ript to analyze country extraction accuracy in the merged dataset.
Focuses on identifying misclassifications and understanding root causes.
"""

import pandas as pd
import re
import pycountry
from collections import defaultdict, Counter

def load_merged_data():
    """Load the merged dataset"""
    file_path = "data/complete_merged_data_20250526_175235.xlsx"
    try:
        df = pd.read_excel(file_path)
        print(f"✓ Successfully loaded dataset: {df.shape}")
        return df
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return None

def examine_specific_row(df, row_number):
    """Examine a specific row in detail"""
    print(f"\n🔍 DETAILED ANALYSIS OF ROW {row_number}")
    print("=" * 60)
    
    if row_number >= len(df):
        print(f"❌ Row {row_number} doesn't exist. Dataset has {len(df)} rows.")
        return
    
    row = df.iloc[row_number]
    
    # Key fields to examine
    key_fields = ['Title', 'Description', 'Extracted Country', 'Post type', 'Publish time']
    
    for field in key_fields:
        if field in df.columns:
            value = row[field]
            print(f"{field}: {value}")
        else:
            print(f"{field}: [Column not found]")
    
    # Combine title and description for analysis
    title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
    description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
    
    combined_text = ""
    if title and description:
        combined_text = f"{title}. {description}"
    elif title:
        combined_text = title
    elif description:
        combined_text = description
    
    print(f"\nCombined Text for Processing:")
    print(f"'{combined_text}'")
    
    # Look for country mentions in the text
    print(f"\n🔍 COUNTRY ANALYSIS:")
    extracted_country = row.get('Extracted Country', 'N/A')
    print(f"Extracted Country: {extracted_country}")
    
    # Check for obvious country mentions
    country_mentions = find_country_mentions_in_text(combined_text)
    if country_mentions:
        print(f"Countries found in text: {country_mentions}")
        
        # Check if extracted country matches any found countries
        if extracted_country not in country_mentions and extracted_country != 'N/A':
            print(f"⚠️  POTENTIAL MISCLASSIFICATION:")
            print(f"   Extracted: {extracted_country}")
            print(f"   Found in text: {country_mentions}")
    else:
        print("No obvious country mentions found in text")
    
    # Check for hashtags specifically
    hashtags = extract_hashtags(combined_text)
    if hashtags:
        print(f"Hashtags found: {hashtags}")
        hashtag_countries = []
        for hashtag in hashtags:
            countries_in_hashtag = find_country_mentions_in_text(hashtag)
            hashtag_countries.extend(countries_in_hashtag)
        
        if hashtag_countries:
            print(f"Countries in hashtags: {hashtag_countries}")

def find_country_mentions_in_text(text):
    """Find potential country mentions in text"""
    if not text or pd.isna(text):
        return []
    
    text_lower = text.lower()
    found_countries = []
    
    # Get all country names from pycountry
    try:
        for country in pycountry.countries:
            # Check official name
            if country.name.lower() in text_lower:
                found_countries.append(country.name)
            
            # Check common name if available
            if hasattr(country, 'common_name') and country.common_name.lower() in text_lower:
                found_countries.append(country.common_name)
    except:
        pass
    
    # Also check for some common country mentions that might not be in pycountry format
    common_countries = {
        'indonesia': 'Indonesia',
        'costa rica': 'Costa Rica',
        'united states': 'United States',
        'usa': 'United States',
        'uk': 'United Kingdom',
        'britain': 'United Kingdom'
    }
    
    for key, value in common_countries.items():
        if key in text_lower:
            found_countries.append(value)
    
    return list(set(found_countries))

def extract_hashtags(text):
    """Extract hashtags from text"""
    if not text or pd.isna(text):
        return []
    
    # Find hashtags using regex
    hashtags = re.findall(r'#\w+', text)
    return hashtags

def analyze_hashtag_accuracy(df):
    """Analyze accuracy specifically for posts with hashtags"""
    print(f"\n📊 HASHTAG ACCURACY ANALYSIS")
    print("=" * 50)
    
    hashtag_issues = []
    
    for idx, row in df.iterrows():
        title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
        description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
        
        combined_text = ""
        if title and description:
            combined_text = f"{title}. {description}"
        elif title:
            combined_text = title
        elif description:
            combined_text = description
        
        hashtags = extract_hashtags(combined_text)
        if hashtags:
            # Check for countries in hashtags
            hashtag_countries = []
            for hashtag in hashtags:
                countries_in_hashtag = find_country_mentions_in_text(hashtag)
                hashtag_countries.extend(countries_in_hashtag)
            
            if hashtag_countries:
                extracted_country = row.get('Extracted Country', 'N/A')
                
                # Check if any hashtag country matches extracted country
                hashtag_countries_set = set(hashtag_countries)
                if extracted_country not in hashtag_countries_set and extracted_country != 'N/A':
                    hashtag_issues.append({
                        'row': idx,
                        'extracted': extracted_country,
                        'hashtag_countries': hashtag_countries,
                        'hashtags': hashtags,
                        'text_sample': combined_text[:100] + "..." if len(combined_text) > 100 else combined_text
                    })
    
    print(f"Found {len(hashtag_issues)} potential hashtag-related misclassifications:")
    
    for i, issue in enumerate(hashtag_issues[:10]):  # Show first 10
        print(f"\n{i+1}. Row {issue['row']}:")
        print(f"   Extracted: {issue['extracted']}")
        print(f"   Hashtag Countries: {issue['hashtag_countries']}")
        print(f"   Hashtags: {issue['hashtags']}")
        print(f"   Text: {issue['text_sample']}")
    
    if len(hashtag_issues) > 10:
        print(f"\n... and {len(hashtag_issues) - 10} more issues")
    
    return hashtag_issues

def sample_accuracy_analysis(df, sample_size=50):
    """Perform a broader accuracy analysis on a sample of data"""
    print(f"\n📈 SAMPLE ACCURACY ANALYSIS ({sample_size} rows)")
    print("=" * 50)
    
    # Take a random sample
    sample_df = df.sample(n=min(sample_size, len(df)), random_state=42)
    
    accuracy_issues = []
    
    for idx, row in sample_df.iterrows():
        title = str(row.get('Title', '')).strip() if pd.notna(row.get('Title')) else ""
        description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ""
        
        combined_text = ""
        if title and description:
            combined_text = f"{title}. {description}"
        elif title:
            combined_text = title
        elif description:
            combined_text = description
        
        # Find countries mentioned in text
        text_countries = find_country_mentions_in_text(combined_text)
        extracted_country = row.get('Extracted Country', 'N/A')
        
        if text_countries and extracted_country not in text_countries and extracted_country != 'N/A':
            accuracy_issues.append({
                'row': idx,
                'extracted': extracted_country,
                'text_countries': text_countries,
                'text_sample': combined_text[:150] + "..." if len(combined_text) > 150 else combined_text
            })
    
    print(f"Potential accuracy issues in sample: {len(accuracy_issues)}/{len(sample_df)} ({len(accuracy_issues)/len(sample_df)*100:.1f}%)")
    
    for i, issue in enumerate(accuracy_issues[:5]):  # Show first 5
        print(f"\n{i+1}. Row {issue['row']}:")
        print(f"   Extracted: {issue['extracted']}")
        print(f"   Text Countries: {issue['text_countries']}")
        print(f"   Text: {issue['text_sample']}")
    
    return accuracy_issues

def main():
    """Main analysis function"""
    print("🔍 COUNTRY EXTRACTION ACCURACY ANALYSIS")
    print("=" * 60)
    
    # Load data
    df = load_merged_data()
    if df is None:
        return
    
    print(f"Dataset shape: {df.shape}")
    print(f"Columns with 'Country': {[col for col in df.columns if 'country' in col.lower()]}")
    
    # Examine specific row 118 (remember Python is 0-indexed, so row 118 in Excel is index 117)
    examine_specific_row(df, 117)  # Row 118 in Excel = index 117 in Python
    
    # Analyze hashtag accuracy
    hashtag_issues = analyze_hashtag_accuracy(df)
    
    # Sample accuracy analysis
    accuracy_issues = sample_accuracy_analysis(df)
    
    # Summary
    print(f"\n📋 SUMMARY")
    print("=" * 30)
    print(f"Total rows analyzed: {len(df)}")
    print(f"Hashtag-related issues: {len(hashtag_issues)}")
    print(f"Sample accuracy issues: {len(accuracy_issues)}")
    
    if hashtag_issues or accuracy_issues:
        print(f"\n⚠️  RECOMMENDATIONS:")
        print("1. Review text preprocessing to ensure hashtags are properly handled")
        print("2. Improve country detection in hashtag content")
        print("3. Consider case-insensitive country matching")
        print("4. Review Gemini prompt to emphasize hashtag content")

if __name__ == "__main__":
    main()
