
def validate_country_with_keywords(text, keyword_map):
    if not text or not isinstance(text, str) or text.strip().lower() == 'n/a':
        return "N/A"

    # Convert text to lowercase for case-insensitive matching
    lower_text = text.lower()

    # Sort keywords by length in descending order to match longer phrases first
    # e.g., "united states" before "states" if "states" was a keyword (it isn't here)
    sorted_keywords = sorted(keyword_map.keys(), key=len, reverse=True)

    for keyword in sorted_keywords:
        # Use word boundaries to avoid partial matches (e.g., "usa" in "causal")
        # FIXED: Use single backslash for proper regex
        if re.search(r'\b' + re.escape(keyword) + r'\b', lower_text):
            return keyword_map[keyword]

    return "N/A"
