import os
import time
from playwright.sync_api import sync_playwright

# Path to the Streamlit app
APP_URL = "http://localhost:8501"
SAMPLE_XLSX = os.path.abspath("Data/sample.xlsx")  # You must provide this file

# Helper to start Streamlit app
import subprocess
import signal

def start_streamlit():
    proc = subprocess.Popen(["streamlit", "run", "app.py"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    time.sleep(5)  # Wait for app to start
    return proc

def stop_streamlit(proc):
    proc.send_signal(signal.SIGTERM)
    proc.wait()

# Test 1: API key detection from .env
def test_env_api_key():
    proc = start_streamlit()
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch()
            page = browser.new_page()
            page.goto(APP_URL)
            page.wait_for_selector('text=Gemini API key loaded from .env file.')
            browser.close()
    finally:
        stop_streamlit(proc)

# Test 2: Manual API key entry (simulate missing .env)
def test_manual_api_key():
    # Temporarily rename .env
    if os.path.exists(".env"):
        os.rename(".env", ".env.bak")
    proc = start_streamlit()
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch()
            page = browser.new_page()
            page.goto(APP_URL)
            page.wait_for_selector('text=Enter your Gemini API key:')
            browser.close()
    finally:
        stop_streamlit(proc)
        if os.path.exists(".env.bak"):
            os.rename(".env.bak", ".env")

# Test 3: End-to-end extraction and download
def test_end_to_end():
    proc = start_streamlit()
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch()
            page = browser.new_page()
            page.goto(APP_URL)
            # Upload file
            page.set_input_files('input[type="file"]', SAMPLE_XLSX)
            # Wait for selectbox
            page.wait_for_selector('text=Select the description column:')
            # Select first column (simulate)
            page.select_option('select', index=0)
            # Wait for extract button
            page.click('button:has-text("Extract Countries")')
            # Wait for download button
            page.wait_for_selector('text=Download Updated Excel', timeout=120000)
            browser.close()
    finally:
        stop_streamlit(proc)

# Test 4: Error handling for invalid API key
def test_invalid_api_key():
    # Temporarily rename .env and use a bad key
    if os.path.exists(".env"):
        os.rename(".env", ".env.bak")
    proc = start_streamlit()
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch()
            page = browser.new_page()
            page.goto(APP_URL)
            page.set_input_files('input[type="file"]', SAMPLE_XLSX)
            page.wait_for_selector('text=Select the description column:')
            page.select_option('select', index=0)
            page.fill('input[type="password"]', 'BAD_KEY')
            page.click('button:has-text("Extract Countries")')
            page.wait_for_selector('text=Gemini API error', timeout=60000)
            browser.close()
    finally:
        stop_streamlit(proc)
        if os.path.exists(".env.bak"):
            os.rename(".env.bak", ".env")

# To run: pytest tests/test_app_playwright.py 